// Content script for TradingView parameter detection
console.log('TradingView parameter detection content script loaded');
console.log('Current URL:', window.location.href);
console.log('Page title:', document.title);

// Function to detect strategy parameters
function detectStrategyParameters() {
    console.log('Scanning for strategy parameters...');
    console.log('Document ready state:', document.readyState);
    console.log('Body element exists:', !!document.body);

    // Debug: Log all elements with class containing 'cell'
    const allCellElements = document.querySelectorAll('[class*="cell"]');
    console.log('Found elements with "cell" in class:', allCellElements.length);
    allCellElements.forEach((el, i) => {
        if (i < 5) { // Log first 5 to avoid spam
            console.log(`Cell element ${i}:`, el.className, el.textContent?.trim().substring(0, 50));
        }
    });

    // Debug: Log all numeric inputs
    const allNumericInputs = document.querySelectorAll('input[inputmode="numeric"], input[type="number"]');
    console.log('Found numeric inputs:', allNumericInputs.length);
    allNumericInputs.forEach((input, i) => {
        if (i < 5) { // Log first 5
            console.log(`Numeric input ${i}:`, input.value, input.className);
        }
    });

    const parameters = [];
    
    // Look for strategy settings dialog/window
    // TradingView uses various selectors, we'll try multiple approaches
    
    // Method 1: Look for parameter rows in strategy settings (your provided pattern)
    const parameterRows = document.querySelectorAll('.cell-tBgV1m0B.first-tBgV1m0B');

    parameterRows.forEach(row => {
        try {
            // Get parameter name from the first cell
            const nameElement = row.querySelector('.inner-tBgV1m0B');
            if (!nameElement) return;

            const paramName = nameElement.textContent.trim();
            if (!paramName) return;

            // Look for the corresponding input in the next cell
            const nextCell = row.parentElement?.querySelector('.cell-tBgV1m0B:not(.first-tBgV1m0B)');
            if (!nextCell) return;

            // Check if it has a numeric input
            const numericInput = nextCell.querySelector('input[inputmode="numeric"], input[type="number"]');
            if (numericInput) {
                const currentValue = numericInput.value;

                // Only add if it's a valid numeric parameter
                if (currentValue && !isNaN(parseFloat(currentValue))) {
                    parameters.push({
                        name: paramName,
                        currentValue: parseFloat(currentValue),
                        type: 'numeric'
                    });
                    console.log(`Found numeric parameter: ${paramName} = ${currentValue}`);
                }
            }
        } catch (error) {
            console.error('Error processing parameter row:', error);
        }
    });

    // Method 1b: Alternative approach for the same pattern
    if (parameters.length === 0) {
        const allCells = document.querySelectorAll('.cell-tBgV1m0B');
        for (let i = 0; i < allCells.length - 1; i++) {
            const cell = allCells[i];
            const nextCell = allCells[i + 1];

            if (cell.classList.contains('first-tBgV1m0B')) {
                try {
                    const nameElement = cell.querySelector('.inner-tBgV1m0B');
                    const numericInput = nextCell.querySelector('input[inputmode="numeric"], input[type="number"]');

                    if (nameElement && numericInput) {
                        const paramName = nameElement.textContent.trim();
                        const currentValue = numericInput.value;

                        if (paramName && currentValue && !isNaN(parseFloat(currentValue))) {
                            parameters.push({
                                name: paramName,
                                currentValue: parseFloat(currentValue),
                                type: 'numeric'
                            });
                            console.log(`Found numeric parameter (alt): ${paramName} = ${currentValue}`);
                        }
                    }
                } catch (error) {
                    console.error('Error processing cell pair:', error);
                }
            }
        }
    }
    
    // Method 2: Alternative selector patterns for different TradingView versions
    if (parameters.length === 0) {
        console.log('Trying alternative selectors...');
        
        // Look for input fields with numeric values in strategy dialogs
        const numericInputs = document.querySelectorAll('input[inputmode="numeric"], input[type="number"]');
        
        numericInputs.forEach(input => {
            try {
                // Try to find the associated label/name
                const container = input.closest('[data-qa-id], .input-container, .form-group, .parameter-row');
                if (!container) return;
                
                // Look for label text in various ways
                let paramName = '';
                
                // Check for label element
                const label = container.querySelector('label');
                if (label) {
                    paramName = label.textContent.trim();
                } else {
                    // Check for text in parent elements
                    const textElements = container.querySelectorAll('*');
                    for (let element of textElements) {
                        if (element.children.length === 0 && element.textContent.trim()) {
                            const text = element.textContent.trim();
                            if (text && text !== input.value && text.length < 50) {
                                paramName = text;
                                break;
                            }
                        }
                    }
                }
                
                if (paramName && input.value && !isNaN(parseFloat(input.value))) {
                    // Check if we already have this parameter
                    const exists = parameters.some(p => p.name === paramName);
                    if (!exists) {
                        parameters.push({
                            name: paramName,
                            currentValue: parseFloat(input.value),
                            type: 'numeric'
                        });
                        console.log(`Found numeric parameter (alt method): ${paramName} = ${input.value}`);
                    }
                }
            } catch (error) {
                console.error('Error processing numeric input:', error);
            }
        });
    }
    
    // Method 3: Look for specific TradingView strategy dialog patterns
    if (parameters.length === 0) {
        console.log('Trying TradingView-specific patterns...');
        
        // Look for strategy settings modal/dialog
        const strategyDialog = document.querySelector('[data-name="strategy-properties-dialog"], .dialog, .modal');
        if (strategyDialog) {
            const inputs = strategyDialog.querySelectorAll('input[type="number"], input[inputmode="numeric"]');
            
            inputs.forEach(input => {
                try {
                    // Find the closest row or container
                    const row = input.closest('tr, .row, .form-row, .parameter-row');
                    if (row) {
                        // Look for the parameter name in the same row
                        const textNodes = [];
                        const walker = document.createTreeWalker(
                            row,
                            NodeFilter.SHOW_TEXT,
                            null,
                            false
                        );
                        
                        let node;
                        while (node = walker.nextNode()) {
                            const text = node.textContent.trim();
                            if (text && text !== input.value && text.length > 2 && text.length < 50) {
                                textNodes.push(text);
                            }
                        }
                        
                        if (textNodes.length > 0 && input.value && !isNaN(parseFloat(input.value))) {
                            const paramName = textNodes[0];
                            const exists = parameters.some(p => p.name === paramName);
                            if (!exists) {
                                parameters.push({
                                    name: paramName,
                                    currentValue: parseFloat(input.value),
                                    type: 'numeric'
                                });
                                console.log(`Found parameter (dialog method): ${paramName} = ${input.value}`);
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error processing dialog input:', error);
                }
            });
        }
    }
    
    // Method 4: Generic approach - find all numeric inputs and try to find their labels
    if (parameters.length === 0) {
        console.log('Trying generic input detection...');

        const allInputs = document.querySelectorAll('input');
        console.log('Total inputs found:', allInputs.length);

        allInputs.forEach((input, index) => {
            try {
                // Check if it's a numeric input
                const isNumeric = input.type === 'number' ||
                                input.inputMode === 'numeric' ||
                                input.getAttribute('inputmode') === 'numeric' ||
                                (input.value && !isNaN(parseFloat(input.value)) && input.value.trim() !== '');

                if (isNumeric && input.value) {
                    console.log(`Numeric input ${index}:`, {
                        value: input.value,
                        type: input.type,
                        inputMode: input.inputMode,
                        className: input.className,
                        id: input.id,
                        name: input.name
                    });

                    // Try to find a label for this input
                    let paramName = '';

                    // Method 1: Look for label element
                    const label = document.querySelector(`label[for="${input.id}"]`);
                    if (label) {
                        paramName = label.textContent.trim();
                    }

                    // Method 2: Look in parent elements for text
                    if (!paramName) {
                        let parent = input.parentElement;
                        let depth = 0;
                        while (parent && depth < 5) {
                            const textContent = parent.textContent.trim();
                            // Look for text that's not just the input value
                            if (textContent && textContent !== input.value && textContent.length < 100) {
                                // Extract meaningful text (not just numbers)
                                const words = textContent.split(/\s+/).filter(word =>
                                    word.length > 2 &&
                                    isNaN(parseFloat(word)) &&
                                    !word.includes(input.value)
                                );
                                if (words.length > 0) {
                                    paramName = words.slice(0, 3).join(' '); // Take first few words
                                    break;
                                }
                            }
                            parent = parent.parentElement;
                            depth++;
                        }
                    }

                    // Method 3: Look for nearby text elements
                    if (!paramName) {
                        const nearbyElements = input.parentElement?.querySelectorAll('*') || [];
                        for (let element of nearbyElements) {
                            if (element !== input && element.children.length === 0) {
                                const text = element.textContent.trim();
                                if (text && text !== input.value && text.length > 2 && text.length < 50) {
                                    paramName = text;
                                    break;
                                }
                            }
                        }
                    }

                    if (paramName) {
                        const exists = parameters.some(p => p.name === paramName);
                        if (!exists) {
                            parameters.push({
                                name: paramName,
                                currentValue: parseFloat(input.value),
                                type: 'numeric'
                            });
                            console.log(`Found parameter (generic method): ${paramName} = ${input.value}`);
                        }
                    } else {
                        console.log(`Found numeric input but no label: value=${input.value}, class=${input.className}`);
                    }
                }
            } catch (error) {
                console.error('Error processing input:', error);
            }
        });
    }

    console.log(`Total parameters detected: ${parameters.length}`, parameters);
    return parameters;
}

// Function to save parameters to storage
function saveParameters(parameters) {
    chrome.storage.local.set({ 'tvParameters': parameters }, function() {
        console.log('Parameters saved to storage:', parameters);
    });
}

// Function to scan and save parameters
function scanAndSaveParameters() {
    const parameters = detectStrategyParameters();
    saveParameters(parameters);
    return parameters;
}

// Function to simulate realistic user input
function simulateUserInput(input, newValue) {
    console.log(`Simulating user input for value: ${newValue}`);

    // Focus the input
    input.focus();

    // Select all existing text
    input.select();

    // Clear the input by simulating backspace/delete
    input.value = '';

    // Simulate typing each character
    const valueStr = newValue.toString();
    for (let i = 0; i < valueStr.length; i++) {
        const char = valueStr[i];

        // Simulate keydown
        const keydownEvent = new KeyboardEvent('keydown', {
            key: char,
            code: `Digit${char}`,
            keyCode: char.charCodeAt(0),
            which: char.charCodeAt(0),
            bubbles: true,
            cancelable: true
        });
        input.dispatchEvent(keydownEvent);

        // Update value progressively
        input.value = valueStr.substring(0, i + 1);

        // Simulate input event
        const inputEvent = new Event('input', { bubbles: true, cancelable: true });
        input.dispatchEvent(inputEvent);

        // Simulate keyup
        const keyupEvent = new KeyboardEvent('keyup', {
            key: char,
            code: `Digit${char}`,
            keyCode: char.charCodeAt(0),
            which: char.charCodeAt(0),
            bubbles: true,
            cancelable: true
        });
        input.dispatchEvent(keyupEvent);
    }

    // Final events
    setTimeout(() => {
        input.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));
        input.dispatchEvent(new Event('blur', { bubbles: true }));
    }, 50);
}

// Function to verify parameter update was successful
async function verifyParameterUpdate(input, expectedValue, paramName, maxRetries = 3) {
    for (let retry = 0; retry < maxRetries; retry++) {
        await new Promise(r => setTimeout(r, 200)); // Wait for UI to update

        const currentValue = input.value;
        console.log(`Verification attempt ${retry + 1}: ${paramName} = "${currentValue}" (expected: "${expectedValue}")`);

        if (currentValue == expectedValue) {
            console.log(`✓ Parameter "${paramName}" successfully updated to ${expectedValue}`);
            return true;
        }

        if (retry < maxRetries - 1) {
            console.log(`⚠ Parameter "${paramName}" not updated correctly, retrying...`);
            // Try to update again
            input.focus();
            input.select();
            input.value = expectedValue;
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
            input.blur();
        }
    }

    console.log(`✗ Failed to update parameter "${paramName}" after ${maxRetries} attempts`);
    return false;
}

// Async version of simulateUserInput with proper delays
async function simulateUserInputAsync(input, newValue) {
    console.log(`Simulating user input for value: ${newValue} (async)`);

    return new Promise(async (resolve) => {
        // Focus the input
        input.focus();
        await new Promise(r => setTimeout(r, 150));

        // Select all existing text
        input.select();
        await new Promise(r => setTimeout(r, 100));

        // Clear the input
        input.value = '';

        // Simulate typing each character with delays
        const valueStr = newValue.toString();
        for (let i = 0; i < valueStr.length; i++) {
            const char = valueStr[i];

            // Simulate keydown
            const keydownEvent = new KeyboardEvent('keydown', {
                key: char,
                code: `Digit${char}`,
                keyCode: char.charCodeAt(0),
                which: char.charCodeAt(0),
                bubbles: true,
                cancelable: true
            });
            input.dispatchEvent(keydownEvent);

            // Small delay between keydown and value update
            await new Promise(r => setTimeout(r, 30));

            // Update value progressively
            input.value = valueStr.substring(0, i + 1);

            // Simulate input event
            const inputEvent = new Event('input', { bubbles: true, cancelable: true });
            input.dispatchEvent(inputEvent);

            // Small delay between input and keyup
            await new Promise(r => setTimeout(r, 30));

            // Simulate keyup
            const keyupEvent = new KeyboardEvent('keyup', {
                key: char,
                code: `Digit${char}`,
                keyCode: char.charCodeAt(0),
                which: char.charCodeAt(0),
                bubbles: true,
                cancelable: true
            });
            input.dispatchEvent(keyupEvent);

            // Longer delay between characters for better reliability
            await new Promise(r => setTimeout(r, 80));
        }

        // Final events with longer delays
        await new Promise(r => setTimeout(r, 200));
        input.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));

        await new Promise(r => setTimeout(r, 200));
        input.dispatchEvent(new Event('blur', { bubbles: true }));

        // Additional events that might trigger strategy recalculation
        await new Promise(r => setTimeout(r, 100));

        // Simulate Enter key press (often triggers form submission/application)
        const enterEvent = new KeyboardEvent('keydown', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13,
            which: 13,
            bubbles: true,
            cancelable: true
        });
        input.dispatchEvent(enterEvent);

        await new Promise(r => setTimeout(r, 100));

        // Trigger focusout event (sometimes different from blur)
        input.dispatchEvent(new Event('focusout', { bubbles: true }));

        // Trigger custom events that TradingView might listen for
        await new Promise(r => setTimeout(r, 100));
        input.dispatchEvent(new CustomEvent('parameterchange', {
            bubbles: true,
            detail: { value: newValue, name: input.name || input.id }
        }));

        // Wait longer to ensure TradingView fully processes the change
        await new Promise(r => setTimeout(r, 600));

        console.log(`Completed input simulation for: ${newValue}`);
        resolve();
    });
}

// Function to extract strategy tester results using TradingView API (like Example extension)
function extractStrategyTesterResults() {
    const results = {
        timestamp: new Date().toISOString(),
        totalPL: null,
        maxEquityDropdown: null,
        totalTrades: null,
        profitableTrades: null,
        profitFactor: null
    };

    try {
        // Method 1: Try TradingView API first (most reliable - from Example extension)
        if (typeof TradingViewApi !== 'undefined' &&
            TradingViewApi._chartWidgetCollection &&
            TradingViewApi._chartWidgetCollection.activeChartWidget) {

            console.log('📊 Extracting strategy data via TradingView API...');

            const strategySource = TradingViewApi._chartWidgetCollection.activeChartWidget._value._model.m_model._activeStrategySource._value;

            if (strategySource && strategySource._reportData) {
                const performance = strategySource._reportData.performance;

                const apiResults = {
                    timestamp: new Date().toISOString(),
                    totalPL: (performance.all.netProfitPercent + '%').replace(/,/g, ''),
                    maxEquityDropdown: (performance.maxStrategyDrawDownPercent + '%').replace(/,/g, ''),
                    totalTrades: performance.all.totalTrades.toString().replace(/,/g, ''),
                    profitableTrades: (performance.all.percentProfitable + '%').replace(/,/g, ''),
                    profitFactor: performance.all.profitFactor.toString().replace(/,/g, '')
                };

                console.log('✅ Successfully extracted strategy data via API:', apiResults);
                return apiResults;
            } else {
                console.log('⚠️ Strategy data not available via API, falling back to DOM extraction');
            }
        } else {
            console.log('⚠️ TradingView API not available, using DOM extraction');
        }

        // Method 2: Fallback to DOM extraction
        console.log('📊 Extracting strategy data via DOM...');

        // Total P&L - User specified selector
        const totalPLSelector = '#bottom-area > div.bottom-widgetbar-content.backtesting > div > div > div > div.reportContainer-NyzFj5yn > div.container-AXqPXerm > div:nth-child(1) > div.container-LVMgafTl.positiveValue-ird3aQgt > div.change-LVMgafTl';
        const totalPLElement = document.querySelector(totalPLSelector);

        if (totalPLElement) {
            let totalPLText = totalPLElement.textContent.trim();
            // Fix character encoding issues - replace Unicode minus with regular minus
            totalPLText = totalPLText.replace(/âˆ'/g, '-').replace(/−/g, '-').replace(/\u2212/g, '-');
            // Remove commas to prevent CSV column splitting
            totalPLText = totalPLText.replace(/,/g, '');
            results.totalPL = totalPLText;
        } else {
            // Try alternative selector without positiveValue class
            const altTotalPLSelector = '#bottom-area > div.bottom-widgetbar-content.backtesting > div > div > div > div.reportContainer-NyzFj5yn > div.container-AXqPXerm > div:nth-child(1) > div.container-LVMgafTl > div.change-LVMgafTl';
            const altTotalPLElement = document.querySelector(altTotalPLSelector);
            if (altTotalPLElement) {
                let totalPLText = altTotalPLElement.textContent.trim();
                totalPLText = totalPLText.replace(/âˆ'/g, '-').replace(/−/g, '-').replace(/\u2212/g, '-');
                // Remove commas to prevent CSV column splitting
                totalPLText = totalPLText.replace(/,/g, '');
                results.totalPL = totalPLText;
            }
        }

        // Max Equity Dropdown - User specified selector
        const maxEquitySelector = '#bottom-area > div.bottom-widgetbar-content.backtesting > div > div > div > div.reportContainer-NyzFj5yn > div.container-AXqPXerm > div:nth-child(2) > div.container-LVMgafTl > div.change-LVMgafTl';
        const maxEquityElement = document.querySelector(maxEquitySelector);
        if (maxEquityElement) {
            let maxEquityText = maxEquityElement.textContent.trim();
            // Remove commas to prevent CSV column splitting
            maxEquityText = maxEquityText.replace(/,/g, '');
            results.maxEquityDropdown = maxEquityText;
        }

        // Total Trades - User specified selector
        const totalTradesSelector = '#bottom-area > div.bottom-widgetbar-content.backtesting > div > div > div > div.reportContainer-NyzFj5yn > div.container-AXqPXerm > div:nth-child(3) > div.container-LVMgafTl > div';
        const totalTradesElement = document.querySelector(totalTradesSelector);
        if (totalTradesElement) {
            let totalTradesText = totalTradesElement.textContent.trim();
            // Remove commas from numeric values to prevent CSV column splitting
            totalTradesText = totalTradesText.replace(/,/g, '');
            results.totalTrades = totalTradesText;
        }

        // Profitable Trades - User specified selector
        const profitableTradesSelector = '#bottom-area > div.bottom-widgetbar-content.backtesting > div > div > div > div.reportContainer-NyzFj5yn > div.container-AXqPXerm > div:nth-child(4) > div.container-LVMgafTl > div.value-LVMgafTl';
        const profitableTradesElement = document.querySelector(profitableTradesSelector);
        if (profitableTradesElement) {
            let profitableTradesText = profitableTradesElement.textContent.trim();
            // Remove commas to prevent CSV column splitting
            profitableTradesText = profitableTradesText.replace(/,/g, '');
            results.profitableTrades = profitableTradesText;
        }

        // Profit Factor - User specified selector
        const profitFactorSelector = '#bottom-area > div.bottom-widgetbar-content.backtesting > div > div > div > div.reportContainer-NyzFj5yn > div.container-AXqPXerm > div:nth-child(5) > div.container-LVMgafTl > div';
        const profitFactorElement = document.querySelector(profitFactorSelector);
        if (profitFactorElement) {
            let profitFactorText = profitFactorElement.textContent.trim();
            // Remove commas to prevent CSV column splitting
            profitFactorText = profitFactorText.replace(/,/g, '');
            results.profitFactor = profitFactorText;
        }

    } catch (error) {
        console.error('Error extracting strategy tester results:', error);
    }

    return results;
}

// Removed disabled CSV functions for performance

// Dialog functions removed - automatic saving only

// Function to save CSV file to extension's test-data folder
async function saveCSVToExtensionFolder() {
    console.log('� Saving CSV file to extension test-data folder...');

    try {
        // Get CSV data from storage
        const csvData = await new Promise((resolve) => {
            chrome.storage.local.get(['optimizationResults'], (result) => {
                resolve(result.optimizationResults || '');
            });
        });

        if (!csvData) {
            console.log('⚠️ No CSV data found to save');
            return { success: false, message: 'No data to save' };
        }

        // Create descriptive filename with timestamp (Windows-compatible format)
        const now = new Date();
        const timestamp = now.getFullYear() + '-' +
                         String(now.getMonth() + 1).padStart(2, '0') + '-' +
                         String(now.getDate()).padStart(2, '0') + ' ' +
                         String(now.getHours()).padStart(2, '0') + '-' +
                         String(now.getMinutes()).padStart(2, '0') + '-' +
                         String(now.getSeconds()).padStart(2, '0');
        const filename = `TV Test Results - ${timestamp}.csv`;

        // Send message to background script to save file
        const result = await new Promise((resolve) => {
            chrome.runtime.sendMessage({
                action: 'saveCSVToExtension',
                csvData: csvData,
                filename: filename
            }, resolve);
        });

        if (result.success) {
            console.log(`✅ CSV file saved successfully: ${filename}`);
            console.log('📁 File location: Extension test-data folder');

            // File saved automatically - no dialog needed

            return {
                success: true,
                message: `CSV file saved: ${filename}`,
                filename: filename,
                downloadId: result.result.downloadId
            };
        } else {
            console.log('⚠️ Failed to save CSV file:', result.error);
            return { success: false, message: result.error };
        }

    } catch (error) {
        console.error('Error saving CSV to extension folder:', error);
        return { success: false, error: error.message };
    }
}

// Dialog functions removed - automatic saving only

// All dialog functions removed - automatic saving only

// Removed disabled CSV download function for performance

// Function to verify if strategy tester values changed between iterations
function verifyStrategyValuesChanged(baseline, current) {
    console.log('🔍 Verifying if strategy values changed...');

    // Compare key values
    const baselinePL = baseline.totalPL || '';
    const currentPL = current.totalPL || '';
    const baselineTrades = baseline.totalTrades || '';
    const currentTrades = current.totalTrades || '';
    const baselinePF = baseline.profitFactor || '';
    const currentPF = current.profitFactor || '';

    console.log('Value comparison:');
    console.log(`  Total P&L: "${baselinePL}" → "${currentPL}"`);
    console.log(`  Total Trades: "${baselineTrades}" → "${currentTrades}"`);
    console.log(`  Profit Factor: "${baselinePF}" → "${currentPF}"`);

    // Check if any key values changed
    const plChanged = baselinePL !== currentPL;
    const tradesChanged = baselineTrades !== currentTrades;
    const pfChanged = baselinePF !== currentPF;

    const anyChanged = plChanged || tradesChanged || pfChanged;

    console.log(`Values changed: P&L=${plChanged}, Trades=${tradesChanged}, PF=${pfChanged}, Any=${anyChanged}`);

    return anyChanged;
}

// Function to trigger strategy update without clicking Apply button (keeps dialog open)
async function triggerStrategyUpdateWithoutApply(inputElement, paramName) {
    console.log(`🔄 Triggering strategy update for ${paramName} without Apply button...`);

    try {
        // Method 1: Enhanced input events
        console.log('Method 1: Enhanced input events...');

        // Focus the input
        inputElement.focus();
        await new Promise(resolve => setTimeout(resolve, 100));

        // Trigger comprehensive events
        const events = [
            new Event('input', { bubbles: true, cancelable: true }),
            new Event('change', { bubbles: true, cancelable: true }),
            new Event('blur', { bubbles: true }),
            new Event('focusout', { bubbles: true }),
            new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }),
            new KeyboardEvent('keyup', { key: 'Enter', bubbles: true }),
            new CustomEvent('parameterChanged', { bubbles: true, detail: { name: paramName } }),
            new CustomEvent('strategyParameterUpdate', { bubbles: true })
        ];

        for (const event of events) {
            inputElement.dispatchEvent(event);
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        // Method 2: Try to trigger global strategy events
        console.log('Method 2: Global strategy events...');

        const globalEvents = [
            new CustomEvent('strategyUpdate', { bubbles: true }),
            new CustomEvent('parameterChange', { bubbles: true }),
            new CustomEvent('recalculate', { bubbles: true }),
            new Event('change', { bubbles: true })
        ];

        for (const event of globalEvents) {
            document.dispatchEvent(event);
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Method 3: Try to find and trigger strategy tester refresh without Apply
        console.log('Method 3: Strategy tester refresh...');

        // Look for refresh/recalculate buttons that don't close the dialog
        const refreshButtons = document.querySelectorAll('button, .button, [role="button"]');
        for (const button of refreshButtons) {
            const text = button.textContent?.trim().toLowerCase();
            const visible = button.offsetParent !== null;

            if (visible && text && (text.includes('refresh') || text.includes('recalculate') || text.includes('update')) &&
                !text.includes('apply') && !text.includes('ok') && !text.includes('cancel')) {
                console.log(`Found refresh button: "${text}"`);
                button.click();
                await new Promise(resolve => setTimeout(resolve, 500));
                break;
            }
        }

        // Method 3.5: Try to trigger strategy recalculation by simulating form submission
        console.log('Method 3.5: Form submission simulation...');
        const form = inputElement.closest('form');
        if (form) {
            console.log('Found form containing input - triggering submit event');
            form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        // Method 3.6: Try to find strategy tester panel and trigger refresh
        console.log('Method 3.6: Strategy tester panel refresh...');
        const strategyTesterPanel = document.querySelector('[data-name="backtesting"], .backtesting, #bottom-area');
        if (strategyTesterPanel) {
            console.log('Found strategy tester panel - triggering refresh events');
            strategyTesterPanel.dispatchEvent(new CustomEvent('refresh', { bubbles: true }));
            strategyTesterPanel.dispatchEvent(new CustomEvent('recalculate', { bubbles: true }));
            await new Promise(resolve => setTimeout(resolve, 300));
        }

        // Method 4: Simulate Tab key to move focus and trigger validation
        console.log('Method 4: Tab key simulation...');

        const tabEvent = new KeyboardEvent('keydown', {
            key: 'Tab',
            code: 'Tab',
            keyCode: 9,
            which: 9,
            bubbles: true
        });
        inputElement.dispatchEvent(tabEvent);

        await new Promise(resolve => setTimeout(resolve, 200));

        console.log('✅ All update methods completed');
        return true;

    } catch (error) {
        console.error('Error in triggerStrategyUpdateWithoutApply:', error);
        return false;
    }
}

// Function to force apply parameters by clicking Apply button (DEPRECATED - causes dialog to close)
async function forceApplyParameters() {
    console.log('🔍 Looking for Apply/OK button to force parameter application...');

    try {
        // Look for Apply/OK buttons in the strategy settings dialog

        // Find all visible buttons
        const allButtons = document.querySelectorAll('button, .button, [role="button"]');
        let applyButton = null;

        console.log(`Found ${allButtons.length} total buttons, checking for Apply/OK...`);

        // Look for buttons by text content
        for (const button of allButtons) {
            const text = button.textContent?.trim().toLowerCase();
            const visible = button.offsetParent !== null;

            if (visible && text && (text.includes('apply') || text.includes('ok') || text.includes('save') ||
                       text.includes('confirm') || text.includes('submit'))) {
                // Prefer Apply over OK over Save
                if (!applyButton ||
                    (text.includes('apply') && !applyButton.textContent.toLowerCase().includes('apply')) ||
                    (text.includes('ok') && applyButton.textContent.toLowerCase().includes('save'))) {
                    applyButton = button;
                }
            }
        }

        if (applyButton) {
            console.log(`🎯 Clicking Apply button: "${applyButton.textContent.trim()}"`);

            // Check if dialog is open before clicking
            const dialogBeforeClick = document.querySelector('.dialog, .modal, [data-name*="dialog"], [data-name*="modal"]');
            const dialogOpenBefore = dialogBeforeClick && dialogBeforeClick.offsetParent !== null;
            console.log(`Dialog open before Apply click: ${dialogOpenBefore}`);

            // Click the Apply button
            applyButton.focus();
            await new Promise(resolve => setTimeout(resolve, 200));
            applyButton.click();

            // Wait a moment and check if dialog is still open
            await new Promise(resolve => setTimeout(resolve, 1000));

            const dialogAfterClick = document.querySelector('.dialog, .modal, [data-name*="dialog"], [data-name*="modal"]');
            const dialogOpenAfter = dialogAfterClick && dialogAfterClick.offsetParent !== null;
            console.log(`Dialog open after Apply click: ${dialogOpenAfter}`);

            if (dialogOpenBefore && !dialogOpenAfter) {
                console.log('⚠️ WARNING: Strategy settings dialog closed after Apply click!');
                console.log('🔄 This will break the optimization process');
                console.log('💡 Try using a different strategy or TradingView version');
                return false; // Indicate that dialog closed
            }

            console.log('✅ Apply button clicked successfully - dialog remains open');
            return true;
        } else {
            console.log('❌ No Apply/OK button found');

            // No Apply/OK button found

            return false;
        }

    } catch (error) {
        console.error('Error in forceApplyParameters:', error);
        return false;
    }
}

// Function to trigger strategy recalculation without closing the dialog
async function triggerStrategyRecalculation() {
    console.log('🔍 Attempting to trigger strategy recalculation without closing dialog...');

    // Method 1: Try to trigger recalculation through DOM events
    try {
        console.log('Method 1: Triggering global recalculation events...');

        // Dispatch custom events that might trigger strategy recalculation
        const events = [
            new CustomEvent('strategyParametersChanged', { bubbles: true }),
            new CustomEvent('parameterUpdate', { bubbles: true }),
            new CustomEvent('strategyRecalculate', { bubbles: true }),
            new Event('change', { bubbles: true }),
            new Event('input', { bubbles: true })
        ];

        for (const event of events) {
            document.dispatchEvent(event);
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Wait for potential recalculation
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log('✅ Global events dispatched');

    } catch (error) {
        console.log('⚠️ Method 1 failed:', error.message);
    }

    // Method 2: Try to find and trigger specific TradingView update mechanisms
    try {
        console.log('Method 2: Looking for TradingView-specific update triggers...');

        // Look for elements that might trigger updates when clicked/focused
        const updateTriggers = [
            // Strategy tester elements
            '[data-name*="backtesting"]',
            '[data-name*="strategy-tester"]',
            '.strategy-tester',
            '.backtesting-panel',

            // Chart elements that might trigger recalculation
            '[data-name*="chart"]',
            '.chart-container',

            // Any visible input that might trigger updates
            'input[inputmode="numeric"]:not([disabled])',
            'input[type="number"]:not([disabled])'
        ];

        for (const selector of updateTriggers) {
            const elements = document.querySelectorAll(selector);
            for (const element of elements) {
                if (element.offsetParent !== null) {
                    // Trigger focus/blur cycle to potentially trigger updates
                    element.focus();
                    await new Promise(resolve => setTimeout(resolve, 50));
                    element.blur();
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
            }
        }

        console.log('✅ TradingView-specific triggers attempted');

    } catch (error) {
        console.log('⚠️ Method 2 failed:', error.message);
    }

    // Method 3: Try to find a "Refresh" or "Update" button that doesn't close the dialog
    try {
        console.log('Method 3: Looking for refresh/update buttons...');

        const allButtons = document.querySelectorAll('button, .button, [role="button"]');
        let refreshButton = null;

        for (const button of allButtons) {
            const text = button.textContent?.trim().toLowerCase();
            const visible = button.offsetParent !== null;
            const enabled = !button.disabled;

            if (visible && enabled && text) {
                // Look for refresh/update buttons but avoid Apply/OK/Cancel
                if ((text.includes('refresh') || text.includes('update') || text.includes('recalculate')) &&
                    !text.includes('apply') && !text.includes('ok') && !text.includes('cancel')) {
                    refreshButton = button;
                    console.log(`Found refresh button: "${button.textContent.trim()}"`);
                    break;
                }
            }
        }

        if (refreshButton) {
            console.log('🔄 Clicking refresh button...');
            refreshButton.click();
            await new Promise(resolve => setTimeout(resolve, 1000));
            console.log('✅ Refresh button clicked');
        } else {
            console.log('No refresh button found');
        }

    } catch (error) {
        console.log('⚠️ Method 3 failed:', error.message);
    }

    // Method 4: Force re-trigger all input events on updated parameters
    try {
        console.log('Method 4: Re-triggering events on all updated parameters...');

        const parameterInputs = document.querySelectorAll('.cell-tBgV1m0B input[inputmode="numeric"], .cell-tBgV1m0B input[type="number"]');

        for (const input of parameterInputs) {
            if (input.offsetParent !== null) {
                // Re-trigger comprehensive events
                input.focus();
                await new Promise(resolve => setTimeout(resolve, 50));

                input.dispatchEvent(new Event('input', { bubbles: true }));
                await new Promise(resolve => setTimeout(resolve, 50));

                input.dispatchEvent(new Event('change', { bubbles: true }));
                await new Promise(resolve => setTimeout(resolve, 50));

                input.blur();
                await new Promise(resolve => setTimeout(resolve, 50));
            }
        }

        console.log('✅ Parameter input events re-triggered');

    } catch (error) {
        console.log('⚠️ Method 4 failed:', error.message);
    }

    // Final wait for any updates to process - reduced since we apply immediately per parameter
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('✅ All parameters have been applied individually - no final Apply needed');

    // Verify dialog is still open
    const dialogStillOpen = document.querySelector('.dialog, .modal, [data-name*="dialog"], [data-name*="modal"]');
    const isDialogVisible = dialogStillOpen && dialogStillOpen.offsetParent !== null;

    if (isDialogVisible) {
        console.log('✅ Strategy settings dialog remains open');
        return {
            success: true,
            message: 'Strategy recalculation triggered - dialog remains open for next iteration'
        };
    } else {
        console.log('⚠️ Dialog appears to have closed unexpectedly');
        return {
            success: false,
            message: 'Dialog closed - may need to reopen for next iteration'
        };
    }
}

// TARGETED APPROACH: Only trigger recalculation on specific updated parameters
async function triggerStrategyRecalculationTargeted(updatedParameters) {
    console.log('🎯 TARGETED: Triggering recalculation only on updated parameters...');
    console.log('Updated parameters:', Object.keys(updatedParameters));

    try {
        // Step 1: Find ONLY the parameter inputs that were actually updated
        console.log('🔍 Finding ONLY updated parameter inputs...');

        const cachedParams = cacheParameterLocations();
        const updatedInputs = [];

        for (const paramName of Object.keys(updatedParameters)) {
            const input = cachedParams.get(paramName);
            if (input && input.offsetParent !== null) {
                updatedInputs.push({ name: paramName, input: input });
                console.log(`✅ Found updated parameter input: "${paramName}"`);
            } else {
                console.log(`⚠️ Could not find input for updated parameter: "${paramName}"`);
            }
        }

        if (updatedInputs.length === 0) {
            console.log('❌ No updated parameter inputs found');
            return {
                success: false,
                message: 'No updated parameter inputs found'
            };
        }

        console.log(`Found ${updatedInputs.length} updated parameter inputs (out of ${Object.keys(updatedParameters).length} total updates)`);

        // Step 2: Enhanced event triggering ONLY on updated inputs
        console.log('⚡ Triggering enhanced events ONLY on updated parameter inputs...');

        for (const { name, input } of updatedInputs) {
            console.log(`🔄 Processing events for "${name}"...`);

            // Focus and trigger comprehensive events
            input.focus();
            await new Promise(resolve => setTimeout(resolve, 100));

            // Multiple event types to ensure TradingView recognizes the change
            const events = [
                new Event('input', { bubbles: true }),
                new Event('change', { bubbles: true }),
                new Event('blur', { bubbles: true }),
                new KeyboardEvent('keydown', { key: 'Enter', keyCode: 13, bubbles: true }),
                new KeyboardEvent('keyup', { key: 'Enter', keyCode: 13, bubbles: true }),
                new KeyboardEvent('keydown', { key: 'Tab', keyCode: 9, bubbles: true })
            ];

            for (const event of events) {
                input.dispatchEvent(event);
                await new Promise(resolve => setTimeout(resolve, 50));
            }

            console.log(`⚡ Enhanced events dispatched for "${name}"`);
        }

        // Step 3: Alternative recalculation triggers
        console.log('🔄 Triggering alternative recalculation methods...');

        // Method A: Try form-level events
        const paramDialog = document.querySelector("[data-name='indicator-properties-dialog']");
        if (paramDialog) {
            const forms = paramDialog.querySelectorAll('form');
            for (const form of forms) {
                form.dispatchEvent(new Event('change', { bubbles: true }));
                form.dispatchEvent(new Event('input', { bubbles: true }));
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        // Method B: REMOVED - Don't click strategy tester (causes window to close/reopen)
        console.log('🔄 Skipping strategy tester click to keep window open...');

        // Method C: Try global document events
        document.dispatchEvent(new CustomEvent('strategyParametersChanged', { bubbles: true }));
        document.dispatchEvent(new Event('change', { bubbles: true }));

        // Step 4: Wait for strategy calculation
        console.log('⏳ Waiting for strategy calculation to complete...');

        // Wait for TradingView to process the changes
        await new Promise(resolve => setTimeout(resolve, 2000)); // Reduced from 3000ms

        const calculationComplete = await waitForStrategyCalculationComplete();

        if (!calculationComplete) {
            console.log('⚠️ Strategy calculation timeout - proceeding anyway');
        } else {
            console.log('✅ Strategy calculation completed successfully');
        }

        // Step 5: Verify windows status (both should be open)
        const dialogStillOpen = document.querySelector("[data-name='indicator-properties-dialog']");
        const strategyTesterOpen = document.querySelector('[data-name*="backtesting"], .strategy-tester, #bottom-area');

        const isDialogVisible = dialogStillOpen && dialogStillOpen.offsetParent !== null;
        const isStrategyTesterVisible = strategyTesterOpen && strategyTesterOpen.offsetParent !== null;

        console.log(`✅ Status check - Dialog: ${isDialogVisible}, Strategy Tester: ${isStrategyTesterVisible}`);

        // CRITICAL: Warn if strategy tester closed unexpectedly
        if (!isStrategyTesterVisible) {
            console.log('⚠️ WARNING: Strategy Tester window appears to be closed!');
            console.log('💡 This may affect result extraction. Please ensure Strategy Tester stays open.');
        } else {
            console.log('✅ Strategy Tester window is open and ready for result extraction');
        }

        return {
            success: true,
            message: `Targeted recalculation completed for ${updatedInputs.length} parameters - dialog should remain open`,
            dialogOpen: isDialogVisible,
            strategyTesterOpen: isStrategyTesterVisible,
            calculationComplete: calculationComplete,
            needsReopen: false,
            updatedInputsCount: updatedInputs.length
        };

    } catch (error) {
        console.log('❌ Error in targeted recalculation:', error.message);
        return {
            success: false,
            message: error.message
        };
    }
}

// SIMPLE APPROACH: Just trigger Enter key on parameter inputs (like Example extension)
async function triggerStrategyRecalculationLikeExample() {
    console.log('🔄 SIMPLE: Using Example extension approach - Enter key on parameter inputs...');

    try {
        // Step 1: Find all parameter inputs that were updated
        console.log('🔍 Finding parameter inputs...');

        const parameterInputs = document.querySelectorAll('.cell-tBgV1m0B input[inputmode="numeric"], .cell-tBgV1m0B input[type="number"]');

        if (parameterInputs.length === 0) {
            console.log('❌ No parameter inputs found');
            return {
                success: false,
                message: 'No parameter inputs found'
            };
        }

        console.log(`Found ${parameterInputs.length} parameter inputs`);

        // Step 2: Trigger Enter key on each parameter input (like Example extension)
        console.log('⌨️ Triggering Enter key on parameter inputs...');

        for (const input of parameterInputs) {
            if (input.offsetParent !== null) {
                // Focus the input first
                input.focus();
                await new Promise(resolve => setTimeout(resolve, 100));

                // Dispatch Enter key event (exactly like Example extension)
                const enterEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    keyCode: 13,
                    charCode: 13,
                    code: 'Enter',
                    bubbles: true,
                    cancelable: true
                });

                input.dispatchEvent(enterEvent);
                console.log('⌨️ Enter key dispatched on parameter input');

                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }

        // Step 3: Wait for strategy calculation (like Example)
        console.log('⏳ Waiting for strategy calculation to complete...');

        const calculationComplete = await waitForStrategyCalculationComplete();

        if (!calculationComplete) {
            console.log('⚠️ Strategy calculation timeout - proceeding anyway');
        } else {
            console.log('✅ Strategy calculation completed successfully');
        }

        // Step 4: Verify windows are still open (should be!)
        const dialogStillOpen = document.querySelector("[data-name='indicator-properties-dialog']");
        const strategyTesterOpen = document.querySelector('[data-name*="backtesting"], .strategy-tester, #bottom-area');

        const isDialogVisible = dialogStillOpen && dialogStillOpen.offsetParent !== null;
        const isStrategyTesterVisible = strategyTesterOpen && strategyTesterOpen.offsetParent !== null;

        console.log(`✅ Status check - Dialog: ${isDialogVisible}, Strategy Tester: ${isStrategyTesterVisible}`);

        return {
            success: true,
            message: 'Enter key approach completed - both windows should remain open',
            dialogOpen: isDialogVisible,
            strategyTesterOpen: isStrategyTesterVisible,
            calculationComplete: calculationComplete
        };

    } catch (error) {
        console.log('❌ Error in Enter key approach:', error.message);
        return {
            success: false,
            message: error.message
        };
    }
}

// Helper function: Wait for strategy calculation (from Example extension)
async function waitForStrategyCalculationComplete() {
    console.log('⏳ Waiting for strategy calculation to complete...');

    try {
        let attempts = 0;
        const maxAttempts = 60; // 30 seconds max wait

        while (attempts < maxAttempts) {
            // Check if strategy is still loading using TradingView API
            try {
                const isLoading = checkIfStrategyIsLoading();

                if (!isLoading) {
                    console.log('✅ Strategy calculation completed');
                    // Wait a bit more to ensure data is ready
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    return true;
                }

                console.log(`⏳ Strategy still calculating... (attempt ${attempts + 1}/${maxAttempts})`);

            } catch (error) {
                // If API check fails, just wait and continue
                console.log('⚠️ Could not check strategy status via API, continuing...');
            }

            await new Promise(resolve => setTimeout(resolve, 500));
            attempts++;
        }

        console.log('⚠️ Timeout waiting for strategy calculation');
        return false;

    } catch (error) {
        console.log('❌ Error waiting for strategy calculation:', error.message);
        return false;
    }
}

// Helper function: Check if strategy is loading (from Example extension)
function checkIfStrategyIsLoading() {
    try {
        // Try to access TradingView's internal API
        if (typeof TradingViewApi !== 'undefined' &&
            TradingViewApi._chartWidgetCollection &&
            TradingViewApi._chartWidgetCollection.activeChartWidget) {

            const statusView = TradingViewApi._chartWidgetCollection.activeChartWidget._value._model.m_model._activeStrategySource._value._statusView;
            const statusText = statusView ? statusView._text : "";
            const isLoading = statusText.includes("loading...");

            if (isLoading) {
                console.log('⏳ Strategy is still calculating via API...');
            } else {
                console.log('✅ Strategy calculation complete via API');
            }

            return isLoading;
        } else {
            // Fallback: assume not loading if API not available
            console.log('⚠️ TradingView API not accessible, assuming calculation complete');
            return false;
        }
    } catch (error) {
        console.log('⚠️ Error checking strategy status via API:', error.message);
        return false; // Assume not loading if error
    }
}

// Function to reopen strategy settings dialog after Apply button closes it
async function reopenStrategySettingsDialog() {
    console.log('🔄 Attempting to reopen strategy settings dialog...');

    try {
        // Method 1: Look for gear/settings icon on the strategy
        const settingsIcons = document.querySelectorAll('[data-name*="settings"], .settings-icon, .gear-icon, [title*="Settings"], [title*="Properties"]');

        for (const icon of settingsIcons) {
            if (icon.offsetParent !== null && !icon.disabled) {
                console.log('Found settings icon, clicking...');
                icon.click();
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Check if dialog opened
                const dialog = document.querySelector('[data-name*="indicator-properties-dialog"], .dialog, .modal');
                if (dialog && dialog.offsetParent !== null) {
                    console.log('✅ Strategy settings dialog reopened via settings icon');
                    return { success: true, method: 'settings-icon' };
                }
            }
        }

        // Method 2: Double-click on strategy name in legend
        console.log('Method 2: Double-clicking on strategy in legend...');
        const legendItems = document.querySelectorAll('.legend-item, .study-legend-item, [data-name*="legend"]');

        for (const item of legendItems) {
            const text = item.textContent?.trim();
            if (item.offsetParent !== null && text && text.length > 0) {
                console.log(`Double-clicking on legend item: "${text}"`);

                const doubleClickEvent = new MouseEvent('dblclick', {
                    bubbles: true,
                    cancelable: true
                });

                item.dispatchEvent(doubleClickEvent);
                await new Promise(resolve => setTimeout(resolve, 1000));

                const dialog = document.querySelector('[data-name*="indicator-properties-dialog"], .dialog, .modal');
                if (dialog && dialog.offsetParent !== null) {
                    console.log('✅ Strategy settings dialog reopened via legend double-click');
                    return { success: true, method: 'legend-doubleclick' };
                }
            }
        }

        // Method 3: Right-click on strategy and select settings
        console.log('Method 3: Right-clicking on strategy elements...');
        const strategyElements = document.querySelectorAll('.legend-item, .study-legend-item, [data-name*="study"], [data-name*="strategy"]');

        for (const element of strategyElements) {
            if (element.offsetParent !== null) {
                console.log('Right-clicking on strategy element...');

                const rightClickEvent = new MouseEvent('contextmenu', {
                    bubbles: true,
                    cancelable: true,
                    button: 2
                });

                element.dispatchEvent(rightClickEvent);
                await new Promise(resolve => setTimeout(resolve, 500));

                // Look for settings/properties in context menu
                const menuItems = document.querySelectorAll('.context-menu-item, .menu-item, [role="menuitem"]');
                for (const item of menuItems) {
                    const itemText = item.textContent?.trim().toLowerCase();
                    if (itemText?.includes('settings') || itemText?.includes('properties') || itemText?.includes('format')) {
                        console.log(`Clicking context menu: "${itemText}"`);
                        item.click();
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        const dialog = document.querySelector('[data-name*="indicator-properties-dialog"], .dialog, .modal');
                        if (dialog && dialog.offsetParent !== null) {
                            console.log('✅ Strategy settings dialog reopened via context menu');
                            return { success: true, method: 'context-menu' };
                        }
                    }
                }
            }
        }

        console.log('❌ Could not reopen strategy settings dialog');
        return { success: false, message: 'No method worked to reopen dialog' };

    } catch (error) {
        console.log('❌ Error reopening dialog:', error.message);
        return { success: false, message: error.message };
    }
}

// Global variables for optimization session management
let lastProcessedParameters = null;
let lastProcessedTime = 0;
let optimizationResults = []; // Array to store all results in memory
let optimizationInProgress = false;

// PERFORMANCE: Enhanced caching to reduce DOM queries
let parameterCache = new Map();
let cacheValid = false;
let domQueryCache = new Map();
let lastCacheTime = 0;

// PERFORMANCE: Fast parameter caching function with throttling
function cacheParameterLocations() {
    const now = Date.now();

    // Throttle cache updates to max once per 500ms
    if (cacheValid && parameterCache.size > 0 && (now - lastCacheTime) < 500) {
        return parameterCache;
    }

    parameterCache.clear();
    lastCacheTime = now;

    // Use cached DOM query if available
    let parameterRows = domQueryCache.get('parameterRows');
    if (!parameterRows || (now - lastCacheTime) > 2000) {
        parameterRows = document.querySelectorAll('.cell-tBgV1m0B.first-tBgV1m0B');
        domQueryCache.set('parameterRows', parameterRows);
    }

    for (let rowIndex = 0; rowIndex < parameterRows.length; rowIndex++) {
        const row = parameterRows[rowIndex];
        const nameElement = row.querySelector('.inner-tBgV1m0B');
        if (!nameElement) continue;

        const paramName = nameElement.textContent.trim();

        // Find input element using multiple approaches
        let numericInput = null;
        const nextCell = row.parentElement?.querySelector('.cell-tBgV1m0B:not(.first-tBgV1m0B)');
        if (nextCell) {
            numericInput = nextCell.querySelector('input[inputmode="numeric"], input[type="number"]');
        }

        if (!numericInput) {
            const allRowCells = row.parentElement?.querySelectorAll('.cell-tBgV1m0B');
            if (allRowCells && allRowCells.length > 1) {
                const siblingIndex = Array.from(allRowCells).indexOf(row);
                const nextSibling = allRowCells[siblingIndex + 1];
                if (nextSibling) {
                    numericInput = nextSibling.querySelector('input[inputmode="numeric"], input[type="number"]');
                }
            }
        }

        if (numericInput) {
            parameterCache.set(paramName, numericInput);
        }
    }

    cacheValid = true;
    return parameterCache;
}

// Function to update parameter values in TradingView with delays
async function updateParameterValues(parameterUpdates) {
    // DUPLICATE PREVENTION: Check if we're processing the same parameters too quickly
    const currentTime = Date.now();
    const parameterString = JSON.stringify(parameterUpdates);

    if (lastProcessedParameters === parameterString && (currentTime - lastProcessedTime) < 5000) {
        return {
            updatedCount: 0,
            results: [{ name: 'duplicate', status: 'skipped', message: 'Duplicate parameters detected' }],
            debugInfo: { duplicateSkipped: true },
            dialogClosed: false
        };
    }

    // Update tracking variables
    lastProcessedParameters = parameterString;
    lastProcessedTime = currentTime;

    let updatedCount = 0;
    const results = [];

    // Convert updates to array for sequential processing
    const updateEntries = Object.entries(parameterUpdates);

    // PERFORMANCE: Use cached parameter locations
    const cachedParams = cacheParameterLocations();

    // Process parameters sequentially using cache
    for (let i = 0; i < updateEntries.length; i++) {
        const [paramName, newValue] = updateEntries[i];

        // FAST: Get input element from cache
        const numericInput = cachedParams.get(paramName);

        if (numericInput) {
            try {
                await simulateUserInputAsync(numericInput, newValue);

                // Quick verification
                if (numericInput.value === newValue.toString()) {
                    updatedCount++;
                    results.push({ name: paramName, value: newValue, status: 'updated', method: 'cached' });

                    // Minimal strategy update trigger
                    numericInput.dispatchEvent(new Event('change', { bubbles: true }));

                } else {
                    results.push({ name: paramName, value: newValue, status: 'verification_failed', method: 'cached' });
                }
            } catch (error) {
                results.push({ name: paramName, value: newValue, status: 'error', error: error.message, method: 'cached' });
            }
        } else {
            results.push({ name: paramName, value: newValue, status: 'not_found', method: 'cached' });
        }

        // MINIMAL delay between parameter updates
        if (i < updateEntries.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 50)); // Ultra-short delay
        }
    }

    // Method 2: Alternative approach if Method 1 didn't work
    if (updatedCount === 0) {
        console.log('Trying alternative parameter update method...');

        // Find all numeric inputs and try to match them with parameter names
        const allInputs = document.querySelectorAll('input[inputmode="numeric"], input[type="number"]');

        allInputs.forEach(input => {
            try {
                // Try to find the parameter name associated with this input
                let paramName = '';

                // Look in parent elements for text that matches our parameter names
                let parent = input.parentElement;
                let depth = 0;
                while (parent && depth < 5) {
                    const textContent = parent.textContent.trim();

                    // Check if any of our parameter names are in this text
                    // Use exact matching to avoid false positives
                    for (const [name] of Object.entries(parameterUpdates)) {
                        // Only match if the parameter name is found as a complete word/phrase
                        if (textContent === name || textContent.includes(name + ':') || textContent.includes(name + ' ')) {
                            paramName = name;
                            console.log(`Found exact match for "${name}" in text "${textContent}"`);
                            break;
                        }
                    }

                    if (paramName) break;
                    parent = parent.parentElement;
                    depth++;
                }

                if (paramName && parameterUpdates.hasOwnProperty(paramName)) {
                    const newValue = parameterUpdates[paramName];

                    // Double-check this parameter hasn't already been updated
                    const alreadyUpdated = results.some(r => r.name === paramName && r.status === 'updated');
                    if (alreadyUpdated) {
                        console.log(`Parameter "${paramName}" already updated, skipping duplicate`);
                        return;
                    }

                    console.log(`Updating "${paramName}" from "${input.value}" to "${newValue}"`);

                    // Use realistic user input simulation
                    simulateUserInput(input, newValue);

                    updatedCount++;
                    results.push({ name: paramName, value: newValue, status: 'updated', method: 'alternative' });
                    console.log(`Updated parameter (alt method): ${paramName} = ${newValue}`);
                }
            } catch (error) {
                console.error('Error updating input:', error);
            }
        });
    }

    // Method 3: Try to find inputs by their current values (only if no duplicates)
    if (updatedCount === 0) {
        console.log('Trying value-based parameter update method...');
        debugInfo.method3Attempts++;

        // Get current parameters to match by value
        const currentParams = detectStrategyParameters();
        console.log('Current detected parameters for value matching:', currentParams);

        for (const currentParam of currentParams) {
            if (parameterUpdates.hasOwnProperty(currentParam.name)) {
                const newValue = parameterUpdates[currentParam.name];

                // Check if this parameter was already updated
                const alreadyUpdated = results.some(r => r.name === currentParam.name && r.status === 'updated');
                if (alreadyUpdated) {
                    console.log(`Parameter "${currentParam.name}" already updated, skipping value method`);
                    continue;
                }

                // Find input with the current value - but be more careful about uniqueness
                const inputs = document.querySelectorAll('input[inputmode="numeric"], input[type="number"]');
                let matchingInputs = [];

                for (const input of inputs) {
                    if (parseFloat(input.value) === currentParam.currentValue) {
                        matchingInputs.push(input);
                    }
                }

                // Only update if there's exactly one matching input (to avoid ambiguity)
                if (matchingInputs.length === 1) {
                    const input = matchingInputs[0];
                    console.log(`Updating "${currentParam.name}" via value method from "${input.value}" to "${newValue}"`);

                    // Use realistic user input simulation
                    simulateUserInput(input, newValue);

                    updatedCount++;
                    results.push({ name: currentParam.name, value: newValue, status: 'updated', method: 'value-based' });
                    console.log(`Updated parameter (value method): ${currentParam.name} = ${newValue}`);
                } else {
                    console.log(`Skipping "${currentParam.name}" - found ${matchingInputs.length} inputs with value ${currentParam.currentValue} (ambiguous)`);
                    results.push({ name: currentParam.name, value: newValue, status: 'ambiguous', method: 'value-based' });
                }
            }
        }
    }

    // Reduced logging for performance
    console.log(`✅ Updated ${updatedCount}/${Object.keys(parameterUpdates).length} parameters`);

    // AUTOMATIC STRATEGY TESTER UPDATE: Try to trigger updates without closing dialog
    if (updatedCount > 0) {
        console.log('\n=== AUTOMATIC STRATEGY TESTER UPDATE ===');
        console.log(`✅ Updated ${updatedCount} parameters successfully`);
        console.log('🔄 Attempting to trigger strategy tester update while keeping dialog open...');

        // CRITICAL: Check if dialog is still open
        const dialogCheck = document.querySelector('.dialog, .modal, [data-name*="dialog"], [data-name*="modal"]');
        const dialogStillOpen = dialogCheck && dialogCheck.offsetParent !== null;

        if (!dialogStillOpen) {
            console.log('❌ CRITICAL: Strategy settings dialog has closed!');
            console.log('🔄 This will prevent further optimization iterations');
            console.log('💡 Please reopen the strategy settings dialog to continue');
            return { updatedCount, results, dialogClosed: true };
        } else {
            console.log('✅ Strategy settings dialog is still open - continuing...');
        }

        // CRITICAL FIX: For multiple parameters, trigger recalculation WITHOUT closing dialog
        console.log(`🔄 CRITICAL: Multiple parameters updated (${updatedCount}), triggering recalculation while keeping dialog open...`);

        // Wait for all parameter updates to settle
        await new Promise(resolve => setTimeout(resolve, 1000)); // Longer wait for multiple params

        // TARGETED APPROACH: Only trigger recalculation on updated parameters
        const updateResult = await triggerStrategyRecalculationTargeted(parameterUpdates);

        if (updateResult.success) {
            console.log('✅ Successfully triggered strategy tester update');
            console.log('📊 Strategy tester should now show updated values');
            console.log('🔄 Settings dialog remains open for next iteration');

            // Wait for strategy tester to fully update with new values
            console.log('⏳ Waiting for strategy tester to fully update (2 seconds)...');
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Capture baseline values before extraction attempts
            console.log('📊 Capturing baseline strategy tester values...');
            const baselineResults = extractStrategyTesterResults();
            console.log('Baseline values:', {
                totalPL: baselineResults.totalPL,
                totalTrades: baselineResults.totalTrades,
                profitFactor: baselineResults.profitFactor
            });

            // Single attempt to extract results (no retries for speed)
            console.log('📊 Extracting strategy tester results...');
            let strategyResults = extractStrategyTesterResults();

            if (strategyResults && (strategyResults.totalPL || strategyResults.totalTrades)) {
                // Verify if values actually changed from baseline
                const valuesChanged = verifyStrategyValuesChanged(baselineResults, strategyResults);

                if (!valuesChanged) {
                    console.log('⚠️ WARNING: Strategy tester values appear unchanged!');
                    console.log('🔄 This suggests parameters were not properly applied to strategy');
                    console.log('💡 Possible issues:');
                    console.log('   - Apply button was not clicked');
                    console.log('   - Strategy settings dialog closed unexpectedly');
                    console.log('   - TradingView did not recalculate strategy');
                    console.log('   - Parameters are not affecting this strategy');
                } else {
                    console.log('✅ Strategy tester values changed - parameters were applied successfully');
                }

                console.log('💾 Storing results in memory (will save to CSV when optimization completes)...');

                // Store results in memory array instead of immediately saving to CSV
                const resultEntry = {
                    timestamp: new Date().toISOString(),
                    parameters: { ...parameterUpdates },
                    results: {
                        totalPL: strategyResults.totalPL,
                        maxEquityDropdown: strategyResults.maxEquityDropdown,
                        totalTrades: strategyResults.totalTrades,
                        profitableTrades: strategyResults.profitableTrades,
                        profitFactor: strategyResults.profitFactor
                    }
                };

                optimizationResults.push(resultEntry);
                optimizationInProgress = true;

                // Minimal logging for performance
                console.log(`✅ Iteration ${optimizationResults.length}: P&L ${strategyResults.totalPL}, Trades ${strategyResults.totalTrades}`);
                console.log('📝 Data ready for CSV export by popup script');

                console.log('✅ Iteration completed - continuing optimization...');
            } else {
                console.log('⚠️ No valid strategy results found after all attempts');
                console.log('🔍 This might indicate:');
                console.log('   - Strategy tester is still calculating');
                console.log('   - TradingView interface has changed');
                console.log('   - Strategy tester panel is not visible');
            }

        } else {
            console.log('⚠️ Automatic strategy update failed:', updateResult.message);
            console.log('📋 Strategy tester may need manual refresh or Apply button click');
        }
    }

    console.log('=== END UPDATE SUMMARY ===');
    return { updatedCount, results };
}

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    console.log('Content script received message:', request);

    if (request.action === 'ping') {
        sendResponse({ success: true, message: 'Content script is loaded' });
    } else if (request.action === 'detectParameters') {
        const parameters = scanAndSaveParameters();
        sendResponse({ success: true, parameters: parameters });
    } else if (request.action === 'updateParameters') {
        // Handle async parameter updates
        updateParameterValues(request.parameters).then(result => {
            sendResponse({ success: true, result: result });
        }).catch(error => {
            console.error('Error in updateParameterValues:', error);
            sendResponse({ success: false, error: error.message });
        });
        return true; // Keep message channel open for async response
    } else if (request.action === 'extractResults') {
        // Extract strategy tester results
        const results = extractStrategyTesterResults();
        sendResponse({ success: true, results: results });
    } else if (request.action === 'saveToCSV') {
        // DISABLED: CSV creation now handled by popup script only
        console.log('⚠️ CSV creation disabled in content script - handled by popup');
        sendResponse({ success: false, message: 'CSV creation disabled in content script' });
    } else if (request.action === 'downloadCSV') {
        // DISABLED: CSV download now handled by popup script only
        console.log('⚠️ CSV download disabled in content script - handled by popup');
        sendResponse({ success: false, message: 'CSV download disabled in content script' });
    } else if (request.action === 'cacheParameters') {
        // PERFORMANCE: Pre-cache parameter locations
        try {
            const cache = cacheParameterLocations();
            console.log(`✅ Pre-cached ${cache.size} parameters for optimization`);
            sendResponse({ success: true, cachedCount: cache.size });
        } catch (error) {
            console.log('❌ Parameter pre-caching failed:', error);
            sendResponse({ success: false, error: error.message });
        }
    } else if (request.action === 'reopenDialog') {
        // Reopen strategy settings dialog if it closed after Apply
        reopenStrategySettingsDialog().then(result => {
            sendResponse({ success: result.success, result: result });
        }).catch(error => {
            sendResponse({ success: false, error: error.message });
        });
        return true; // Keep message channel open for async response
    }

    return false; // Don't keep channel open for sync responses
});

// Auto-scan when page loads and when DOM changes
let scanTimeout;

function debouncedScan() {
    clearTimeout(scanTimeout);
    // Use requestIdleCallback for non-critical scanning to avoid blocking main thread
    if (window.requestIdleCallback) {
        window.requestIdleCallback(() => {
            scanAndSaveParameters();
        }, { timeout: 3000 });
    } else {
        scanTimeout = setTimeout(() => {
            scanAndSaveParameters();
        }, 2000);
    }
}

// Initial scan
setTimeout(() => {
    console.log('Performing initial parameter scan...');
    scanAndSaveParameters();
}, 3000);

// Watch for DOM changes (strategy dialog opening/closing)
const observer = new MutationObserver((mutations) => {
    let shouldScan = false;

    mutations.forEach((mutation) => {
        // Check if new nodes were added that might contain strategy parameters
        if (mutation.addedNodes.length > 0) {
            for (let node of mutation.addedNodes) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Check if it's a dialog, modal, or contains input fields
                    if (node.matches && (
                        node.matches('.dialog, .modal, [data-name*="strategy"], [data-name*="properties"]') ||
                        node.querySelector && node.querySelector('input[type="number"], input[inputmode="numeric"]')
                    )) {
                        shouldScan = true;
                        // PERFORMANCE: Invalidate cache when DOM changes
                        cacheValid = false;
                        break;
                    }
                }
            }
        }
    });

    if (shouldScan) {
        console.log('DOM change detected, scheduling parameter scan...');
        debouncedScan();
    }
});

// Start observing
observer.observe(document.body, {
    childList: true,
    subtree: true
});

console.log('TradingView parameter detection initialized');

// CRITICAL DEBUG: Check if script is loading
console.log('� CONTENT SCRIPT STARTING...');
console.log('� Current URL:', window.location.href);
console.log('� Document ready state:', document.readyState);

// Create a simple test object immediately
window.testExtension = {
    loaded: true,
    timestamp: new Date().toISOString(),
    test: function() {
        console.log('✅ Test extension object is working!');
        return 'Extension is loaded and working';
    }
};

console.log('🚨 Test object created:', typeof window.testExtension);

// Initialize optimization system immediately
window.optimizationResults = [];
window.optimizationInProgress = false;

console.log('🚨 Optimization arrays initialized');

// Expose functions to global scope for debugging
window.tvParamDetector = {
    // Simple test function that should always work
    test: function() {
        console.log('✅ tvParamDetector is working!');
        return 'Extension loaded successfully';
    },

    // Automatic parameter detection
    detectParameters: function() {
        console.log('🔍 Detecting strategy parameters...');

        const parameters = [];

        // Look for parameter rows in strategy settings dialog
        const parameterRows = document.querySelectorAll('.cell-tBgV1m0B.first-tBgV1m0B');
        console.log(`Found ${parameterRows.length} potential parameter rows`);

        parameterRows.forEach((row, index) => {
            const nameElement = row.querySelector('.inner-tBgV1m0B');
            const paramName = nameElement ? nameElement.textContent.trim() : null;

            if (paramName) {
                // Look for numeric input in the same row or nearby
                const parentRow = row.closest('tr') || row.parentElement;
                const numericInput = parentRow ? parentRow.querySelector('input[inputmode="numeric"], input[type="number"]') : null;

                if (numericInput) {
                    const currentValue = numericInput.value;
                    parameters.push({
                        name: paramName,
                        element: numericInput,
                        currentValue: currentValue,
                        row: index + 1
                    });
                    console.log(`✅ Found parameter: "${paramName}" = ${currentValue}`);
                } else {
                    console.log(`⚠️ Found parameter name "${paramName}" but no numeric input`);
                }
            }
        });

        console.log(`🎯 Detected ${parameters.length} parameters with inputs`);
        return parameters;
    },

    // Update a single parameter (using cache for speed)
    updateParameter: async function(paramName, newValue) {
        console.log(`🔄 Updating parameter "${paramName}" to ${newValue}...`);

        // Use cached parameter locations for speed
        const cachedParams = cacheParameterLocations();
        let inputElement = cachedParams.get(paramName);

        if (!inputElement) {
            // Fallback to detection if not in cache
            const parameters = this.detectParameters();
            const param = parameters.find(p => p.name === paramName);
            if (!param) {
                console.error(`❌ Parameter "${paramName}" not found`);
                return { success: false, error: 'Parameter not found' };
            }
            inputElement = param.element;
        }

        const oldValue = inputElement.value;

        try {
            // Update the input value
            inputElement.focus();
            await new Promise(resolve => setTimeout(resolve, 50)); // Reduced delay

            inputElement.value = newValue.toString();

            // Trigger events to notify TradingView of the change (optimized)
            const inputEvent = new Event('input', { bubbles: true });
            const changeEvent = new Event('change', { bubbles: true });
            inputElement.dispatchEvent(inputEvent);
            inputElement.dispatchEvent(changeEvent);

            await new Promise(resolve => setTimeout(resolve, 100)); // Reduced delay

            // Verify the update
            if (inputElement.value === newValue.toString()) {
                console.log(`✅ Successfully updated "${paramName}": ${oldValue} → ${newValue}`);

                // Reduced wait for strategy recalculation
                await new Promise(resolve => setTimeout(resolve, 500)); // Reduced from 1000ms

                return { success: true, oldValue: oldValue, newValue: newValue };
            } else {
                console.error(`❌ Update failed: expected "${newValue}", got "${inputElement.value}"`);
                return { success: false, error: 'Value verification failed' };
            }

        } catch (error) {
            console.error(`❌ Error updating parameter:`, error);
            return { success: false, error: error.message };
        }
    },

    // Extract strategy tester results
    extractResults: function() {
        console.log('📊 Extracting strategy tester results...');

        const results = {
            timestamp: new Date().toISOString(),
            totalPL: 'N/A',
            maxEquityDropdown: 'N/A',
            totalTrades: 'N/A',
            profitableTrades: 'N/A',
            profitFactor: 'N/A'
        };

        try {
            // Look for strategy tester results in various possible locations

            // Try to find Total P&L
            const plElements = document.querySelectorAll('*');
            for (const el of plElements) {
                const text = el.textContent?.trim();
                if (text && (text.includes('%') || text.includes('$')) &&
                    (text.includes('+') || text.includes('-')) &&
                    el.offsetParent !== null) {

                    // Check if this looks like a P&L value
                    if (text.match(/[+-]?\d+\.?\d*%/) || text.match(/[+-]?\$\d+/)) {
                        results.totalPL = text;
                        console.log(`Found Total P&L: ${text}`);
                        break;
                    }
                }
            }

            // Try to find Total Trades
            const tradeElements = document.querySelectorAll('*');
            for (const el of tradeElements) {
                const text = el.textContent?.trim();
                if (text && text.match(/^\d+$/) && parseInt(text) > 0 && parseInt(text) < 10000) {
                    const parent = el.parentElement;
                    const context = parent ? parent.textContent.toLowerCase() : '';
                    if (context.includes('trade') || context.includes('order')) {
                        results.totalTrades = text;
                        console.log(`Found Total Trades: ${text}`);
                        break;
                    }
                }
            }

            // Try to find Profit Factor
            const pfElements = document.querySelectorAll('*');
            for (const el of pfElements) {
                const text = el.textContent?.trim();
                if (text && text.match(/^\d+\.?\d*$/) && parseFloat(text) > 0 && parseFloat(text) < 10) {
                    const parent = el.parentElement;
                    const context = parent ? parent.textContent.toLowerCase() : '';
                    if (context.includes('profit') && context.includes('factor')) {
                        results.profitFactor = text;
                        console.log(`Found Profit Factor: ${text}`);
                        break;
                    }
                }
            }

            console.log('📊 Extracted results:', results);
            return results;

        } catch (error) {
            console.error('❌ Error extracting results:', error);
            return results;
        }
    },
    detectParameters: detectStrategyParameters,
    scanAndSave: scanAndSaveParameters,
    updateParameters: updateParameterValues,
    testUpdate: function(paramName, value) {
        const testParams = {};
        testParams[paramName] = value;
        console.log('Testing parameter update:', testParams);
        return updateParameterValues(testParams);
    },
    extractResults: extractStrategyTesterResults,
    saveToCSV: saveResultsToCSV,
    saveToExtension: saveCSVToExtensionFolder,
    testCSV: function() {
        console.log('Testing CSV functionality...');
        const results = extractStrategyTesterResults();
        const testParams = { 'ATR Multiplier': 2.0, 'Test Param': 1.5 };
        return saveResultsToCSV(results, testParams);
    },
    testExtensionSave: function() {
        console.log('Testing extension folder save...');
        return saveCSVToExtensionFolder();
    },
    // Dialog functions removed - automatic saving only
    testFallbackDownload: function() {
        console.log('Testing fallback download...');
        return downloadCSVFallback();
    },
    clearCSVData: function() {
        console.log('Clearing CSV data...');
        chrome.storage.local.set({ 'optimizationResults': '' }, () => {
            console.log('✅ CSV data cleared');
        });
        // Reset duplicate tracking
        lastProcessedParameters = null;
        lastProcessedTime = 0;
        console.log('✅ Duplicate tracking reset');
    },
    resetDuplicateTracking: function() {
        lastProcessedParameters = null;
        lastProcessedTime = 0;
        console.log('✅ Duplicate tracking reset');
    },
    startOptimization: function() {
        console.log('🚀 Starting new optimization session...');
        optimizationResults = [];
        optimizationInProgress = true;
        lastProcessedParameters = null;
        lastProcessedTime = 0;
        console.log('✅ Optimization session initialized');
    },
    finishOptimization: function() {
        console.log('🏁 Finishing optimization session...');
        return { success: true, message: 'Optimization finished - CSV handled by popup script' };
    },
    getStoredResults: function() {
        console.log(`📊 Current stored results: ${optimizationResults.length} iterations`);
        return optimizationResults;
    },
    testMemorySystem: function() {
        console.log('🧪 Testing memory-based optimization system...');

        // Start optimization
        this.startOptimization();

        // Simulate some test results
        const testResults = [
            { parameters: { 'ATR Multiplier': 0.2 }, results: { totalPL: '312.33%', totalTrades: '592', profitFactor: '1.105' } },
            { parameters: { 'ATR Multiplier': 0.3 }, results: { totalPL: '346.35%', totalTrades: '511', profitFactor: '1.093' } },
            { parameters: { 'ATR Multiplier': 0.4 }, results: { totalPL: '170.12%', totalTrades: '986', profitFactor: '1.057' } }
        ];

        // Add test results to memory
        testResults.forEach((result, i) => {
            optimizationResults.push({
                timestamp: new Date().toISOString(),
                parameters: result.parameters,
                results: {
                    totalPL: result.results.totalPL,
                    maxEquityDropdown: '60.00%',
                    totalTrades: result.results.totalTrades,
                    profitableTrades: '35.00%',
                    profitFactor: result.results.profitFactor
                }
            });
            console.log(`Added test result ${i + 1}: ATR Multiplier = ${result.parameters['ATR Multiplier']}`);
        });

        console.log(`✅ Test data added: ${optimizationResults.length} iterations`);
        console.log('💾 Now call finishOptimization() to save CSV file');

        return optimizationResults;
    },
    testCSVSave: async function() {
        console.log('🧪 Testing CSV save functionality...');

        // Add some test data if none exists
        if (optimizationResults.length === 0) {
            console.log('No data found, adding test data...');
            this.testMemorySystem();
        }

        console.log(`Current data: ${optimizationResults.length} iterations`);
        console.log('Attempting to save CSV...');

        try {
            console.log('CSV saving handled by popup script only');
            return { success: true, message: 'CSV handled by popup script' };
        } catch (error) {
            console.error('Save error:', error);
            return { success: false, error: error.message };
        }
    },
    debugDownloads: function() {
        console.log('🔍 Checking Chrome downloads API...');

        if (typeof chrome !== 'undefined' && chrome.runtime) {
            console.log('✅ Chrome extension context available');

            // Test message to background script
            chrome.runtime.sendMessage({ action: 'ping' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('❌ Background script communication failed:', chrome.runtime.lastError);
                } else {
                    console.log('✅ Background script communication working:', response);
                }
            });
        } else {
            console.error('❌ Chrome extension context not available');
        }
    },
    testDirectDownload: async function() {
        console.log('🧪 Testing direct CSV download...');

        // Create test CSV data
        const testCSV = 'Parameter,Value,Result\nATR Multiplier,0.5,312.27%\nATR Multiplier,0.6,346.35%';

        try {
            // Test direct message to background script
            const result = await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    action: 'saveCSVToExtension',
                    csvData: testCSV,
                    filename: 'test-file.csv'
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error('❌ Message failed:', chrome.runtime.lastError);
                        resolve({ success: false, error: chrome.runtime.lastError.message });
                    } else {
                        console.log('📨 Background response:', response);
                        resolve(response);
                    }
                });
            });

            console.log('🔍 Direct download result:', result);
            return result;

        } catch (error) {
            console.error('❌ Direct download error:', error);
            return { success: false, error: error.message };
        }
    },
    testFallbackSave: function() {
        console.log('🧪 Testing fallback CSV save (browser download)...');

        // Create test CSV data
        const testCSV = 'Parameter,Value,Result\nATR Multiplier,0.5,312.27%\nATR Multiplier,0.6,346.35%';

        try {
            // Create blob and download link
            const blob = new Blob([testCSV], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', 'fallback-test.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                // DISABLED: link.click() - causes save dialog
                console.log('⚠️ CSV download disabled in content script - handled by popup script only');
                document.body.removeChild(link);
                return { success: true, message: 'Fallback download triggered' };
            } else {
                console.error('❌ Browser does not support download attribute');
                return { success: false, message: 'Browser does not support download' };
            }

        } catch (error) {
            console.error('❌ Fallback download error:', error);
            return { success: false, error: error.message };
        }
    },
    logDOMInfo: function() {
        console.log('=== DOM DEBUG INFO ===');
        console.log('URL:', window.location.href);
        console.log('Title:', document.title);
        console.log('Body classes:', document.body?.className);

        // Log all elements with 'cell' in class name
        const cellElements = document.querySelectorAll('[class*="cell"]');
        console.log('Elements with "cell" in class:', cellElements.length);

        // Log all numeric inputs
        const numericInputs = document.querySelectorAll('input[inputmode="numeric"], input[type="number"]');
        console.log('Numeric inputs:', numericInputs.length);

        // Log all inputs
        const allInputs = document.querySelectorAll('input');
        console.log('All inputs:', allInputs.length);

        // Try to find strategy-related elements
        const strategyElements = document.querySelectorAll('[class*="strategy"], [data-name*="strategy"], [class*="properties"], [class*="dialog"], [class*="modal"]');
        console.log('Strategy-related elements:', strategyElements.length);

        // Log parameter rows specifically
        const paramRows = document.querySelectorAll('.cell-tBgV1m0B.first-tBgV1m0B');
        console.log('Parameter rows found:', paramRows.length);
        paramRows.forEach((row, i) => {
            const nameEl = row.querySelector('.inner-tBgV1m0B');
            console.log(`Row ${i}: ${nameEl ? nameEl.textContent.trim() : 'No name found'}`);
        });

        return {
            cellElements: cellElements.length,
            numericInputs: numericInputs.length,
            allInputs: allInputs.length,
            strategyElements: strategyElements.length,
            parameterRows: paramRows.length
        };
    },
    saveCurrentResults: function() {
        console.log('💾 Saving current optimization results to Downloads folder...');

        if (optimizationResults.length === 0) {
            console.log('⚠️ No results to save. Run some optimization iterations first.');
            return { success: false, message: 'No results to save' };
        }

        try {
            // Create CSV content
            const allParameterNames = new Set();
            optimizationResults.forEach(result => {
                Object.keys(result.parameters).forEach(paramName => {
                    allParameterNames.add(paramName);
                });
            });

            const parameterNames = Array.from(allParameterNames).sort();

            // Create CSV header
            const csvHeaders = [];
            parameterNames.forEach(paramName => {
                csvHeaders.push(paramName);
            });
            csvHeaders.push('Total P&L');
            csvHeaders.push('Max Equity Dropdown');
            csvHeaders.push('Total Trades');
            csvHeaders.push('Profitable Trades');
            csvHeaders.push('Profit Factor');

            // Create CSV rows
            const csvRows = [];
            csvRows.push(csvHeaders.join(','));

            optimizationResults.forEach(result => {
                const row = [];

                // Add parameter values
                parameterNames.forEach(paramName => {
                    row.push(result.parameters[paramName] || 'N/A');
                });

                // Add result values
                row.push(result.results.totalPL || 'N/A');
                row.push(result.results.maxEquityDropdown || 'N/A');
                row.push(result.results.totalTrades || 'N/A');
                row.push(result.results.profitableTrades || 'N/A');
                row.push(result.results.profitFactor || 'N/A');

                csvRows.push(row.join(','));
            });

            const csvContent = csvRows.join('\n');

            // Create filename with timestamp
            const now = new Date();
            const timestamp = now.getFullYear() + '-' +
                             String(now.getMonth() + 1).padStart(2, '0') + '-' +
                             String(now.getDate()).padStart(2, '0') + ' ' +
                             String(now.getHours()).padStart(2, '0') + '-' +
                             String(now.getMinutes()).padStart(2, '0') + '-' +
                             String(now.getSeconds()).padStart(2, '0');
            const filename = `TV Test Results - ${timestamp}.csv`;

            // Create download link and trigger download
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                // DISABLED: link.click() - causes save dialog
                console.log('⚠️ CSV download disabled in content script - handled by popup script only');
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                console.log(`✅ CSV file downloaded: ${filename}`);
                console.log(`📊 Total iterations saved: ${optimizationResults.length}`);
                console.log('📁 File saved to your Downloads folder');

                // Reset optimization session
                optimizationResults = [];
                optimizationInProgress = false;

                return {
                    success: true,
                    message: `Downloaded ${optimizationResults.length} results as ${filename}`,
                    filename: filename
                };
            } else {
                console.error('❌ Browser does not support download attribute');
                return { success: false, message: 'Browser does not support download' };
            }

        } catch (error) {
            console.error('❌ Save error:', error);
            return { success: false, error: error.message };
        }
    },
    getCurrentResults: function() {
        console.log(`📊 Current optimization results: ${optimizationResults.length} iterations`);
        optimizationResults.forEach((result, i) => {
            const params = Object.keys(result.parameters).map(key => `${key}=${result.parameters[key]}`).join(', ');
            console.log(`${i + 1}. ${params} → P&L: ${result.results.totalPL}`);
        });
        return optimizationResults;
    },

    // Automatic optimization function
    runOptimization: async function(paramName, values) {
        console.log(`🚀 Starting automatic optimization for "${paramName}"...`);
        console.log(`📊 Testing values: [${values.join(', ')}]`);

        // Initialize optimization session
        this.startOptimization();

        let successCount = 0;
        let errorCount = 0;

        for (let i = 0; i < values.length; i++) {
            const value = values[i];
            console.log(`\n=== Iteration ${i + 1}/${values.length}: ${paramName} = ${value} ===`);

            try {
                // Step 1: Update parameter
                const updateResult = await this.updateParameter(paramName, value);

                if (!updateResult.success) {
                    console.error(`❌ Failed to update parameter: ${updateResult.error}`);
                    errorCount++;
                    continue;
                }

                // Step 2: Wait for strategy recalculation
                console.log('⏳ Waiting for strategy recalculation...');
                await new Promise(resolve => setTimeout(resolve, 1500));

                // Step 3: Extract results
                const results = this.extractResults();

                // Step 4: Store in memory
                const resultEntry = {
                    timestamp: new Date().toISOString(),
                    parameters: { [paramName]: value },
                    results: {
                        totalPL: results.totalPL,
                        maxEquityDropdown: results.maxEquityDropdown,
                        totalTrades: results.totalTrades,
                        profitableTrades: results.profitableTrades,
                        profitFactor: results.profitFactor
                    }
                };

                optimizationResults.push(resultEntry);
                successCount++;

                console.log(`✅ Iteration ${i + 1} completed successfully`);
                console.log(`📊 Results: P&L=${results.totalPL}, Trades=${results.totalTrades}, PF=${results.profitFactor}`);

                // Small delay between iterations
                await new Promise(resolve => setTimeout(resolve, 1000));

            } catch (error) {
                console.error(`❌ Error in iteration ${i + 1}:`, error);
                errorCount++;
            }
        }

        console.log(`\n🏁 Optimization completed!`);
        console.log(`✅ Successful iterations: ${successCount}`);
        console.log(`❌ Failed iterations: ${errorCount}`);
        console.log(`📊 Total results stored: ${optimizationResults.length}`);

        // DISABLED: Automatic save - handled by popup script only
        if (optimizationResults.length > 0) {
            console.log('� Results ready for CSV export by popup script');
            // No automatic save - popup script handles CSV creation
        }

        return {
            success: true,
            totalIterations: values.length,
            successfulIterations: successCount,
            failedIterations: errorCount,
            resultsStored: optimizationResults.length
        };
    },

    // Removed test optimization function for performance
};

// Content script fully loaded - reduced logging for performance
console.log('✅ TradingView Optimizer content script loaded');
