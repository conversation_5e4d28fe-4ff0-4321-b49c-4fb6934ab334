document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Popup.js loaded!');

    const detectParamsBtn = document.getElementById('detectParams');
    const testExtractionBtn = document.getElementById('testExtraction');
    const runOptimizationBtn = document.getElementById('runOptimization');
    const statusDiv = document.getElementById('status');

    console.log('Buttons found:', {
        detectParams: !!detectParamsBtn,
        testExtraction: !!testExtractionBtn,
        runOptimization: !!runOptimizationBtn,
        status: !!statusDiv
    });

    function showStatus(message, type = 'info') {
        statusDiv.textContent = message;
        statusDiv.className = type;
        console.log(`[${type.toUpperCase()}] ${message}`);
    }

    function executeScript(func) {
        return new Promise((resolve, reject) => {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (tabs[0]) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: func
                    }, (results) => {
                        if (chrome.runtime.lastError) {
                            reject(chrome.runtime.lastError);
                        } else {
                            resolve(results[0]?.result);
                        }
                    });
                } else {
                    reject(new Error('No active tab found'));
                }
            });
        });
    }

    // Test Extension Function
    async function testExtension() {
        try {
            showStatus('Testing extension...', 'info');
            
            const result = await executeScript(() => {
                // Test if TradingView API is available
                try {
                    if (typeof TradingViewApi !== 'undefined') {
                        return {
                            success: true,
                            message: 'TradingView API detected',
                            hasStrategy: !!TradingViewApi._chartWidgetCollection?.activeChartWidget?._value?._model?.m_model?._activeStrategySource?._value
                        };
                    } else {
                        return {
                            success: false,
                            message: 'TradingView API not found. Make sure you are on TradingView.com'
                        };
                    }
                } catch (error) {
                    return {
                        success: false,
                        message: 'Error accessing TradingView API: ' + error.message
                    };
                }
            });

            if (result.success) {
                showStatus(`✅ ${result.message}. Strategy active: ${result.hasStrategy}`, 'success');
            } else {
                showStatus(`❌ ${result.message}`, 'error');
            }
        } catch (error) {
            showStatus(`❌ Extension test failed: ${error.message}`, 'error');
        }
    }

    // Detect Parameters Function
    async function detectParameters() {
        try {
            showStatus('Detecting parameters...', 'info');
            
            const result = await executeScript(() => {
                try {
                    const paramDialog = document.querySelector("[data-name='indicator-properties-dialog']");
                    if (!paramDialog) {
                        return {
                            success: false,
                            message: 'Strategy settings dialog not found. Please open strategy settings first.'
                        };
                    }

                    const numericInputs = paramDialog.querySelectorAll("[inputmode='numeric']");
                    const parameters = [];

                    numericInputs.forEach((input, index) => {
                        let paramName = `Parameter ${index + 1}`;
                        
                        // Try to find parameter name
                        const row = input.closest('tr, .row, div');
                        if (row) {
                            const labels = row.querySelectorAll('label, span, div');
                            for (const label of labels) {
                                const text = label.textContent?.trim();
                                if (text && text.length > 0 && text.length < 50 && !text.match(/^\d+\.?\d*$/)) {
                                    paramName = text;
                                    break;
                                }
                            }
                        }

                        parameters.push({
                            name: paramName,
                            currentValue: input.value,
                            index: index
                        });
                    });

                    return {
                        success: true,
                        parameters: parameters,
                        count: parameters.length
                    };
                } catch (error) {
                    return {
                        success: false,
                        message: 'Error detecting parameters: ' + error.message
                    };
                }
            });

            if (result.success) {
                let message = `✅ Found ${result.count} parameters:\n`;
                result.parameters.forEach((param, i) => {
                    message += `${i + 1}. "${param.name}" = ${param.currentValue}\n`;
                });
                showStatus(message, 'success');
            } else {
                showStatus(`❌ ${result.message}`, 'error');
            }
        } catch (error) {
            showStatus(`❌ Parameter detection failed: ${error.message}`, 'error');
        }
    }

    // Test Data Extraction Function
    async function testDataExtraction() {
        try {
            showStatus('Testing data extraction...', 'info');
            
            const result = await executeScript(() => {
                try {
                    const strategySource = TradingViewApi._chartWidgetCollection.activeChartWidget._value._model.m_model._activeStrategySource._value;
                    
                    if (!strategySource || !strategySource._reportData) {
                        return {
                            success: false,
                            message: 'No strategy data available. Make sure a strategy is running.'
                        };
                    }
                    
                    const performance = strategySource._reportData.performance;
                    
                    const results = {
                        totalPL: performance.all.netProfitPercent + '%',
                        maxEquityDropdown: performance.maxStrategyDrawDownPercent + '%',
                        totalTrades: performance.all.totalTrades.toString(),
                        profitableTrades: performance.all.percentProfitable + '%',
                        profitFactor: performance.all.profitFactor.toString()
                    };
                    
                    return {
                        success: true,
                        results: results
                    };
                } catch (error) {
                    return {
                        success: false,
                        message: 'Error extracting data: ' + error.message
                    };
                }
            });

            if (result.success) {
                const r = result.results;
                const message = `✅ Strategy Data:\nP&L: ${r.totalPL}\nMax DD: ${r.maxEquityDropdown}\nTrades: ${r.totalTrades}\nWin Rate: ${r.profitableTrades}\nProfit Factor: ${r.profitFactor}`;
                showStatus(message, 'success');
            } else {
                showStatus(`❌ ${result.message}`, 'error');
            }
        } catch (error) {
            showStatus(`❌ Data extraction failed: ${error.message}`, 'error');
        }
    }

    // Run Optimization Function
    async function runOptimization() {
        try {
            showStatus('Starting optimization...', 'info');
            runOptimizationBtn.disabled = true;
            runOptimizationBtn.textContent = 'Running...';

            // Inject the complete optimization system
            const result = await executeScript(() => {
                // COMPLETE OPTIMIZATION SYSTEM - Based on working Example extension
                window.tvOptimizer = {
                    optimizationResults: [],

                    // Access TradingView's internal API for strategy data
                    getStrategyData: function() {
                        try {
                            const strategySource = TradingViewApi._chartWidgetCollection.activeChartWidget._value._model.m_model._activeStrategySource._value;

                            if (!strategySource || !strategySource._reportData) {
                                console.log('⚠️ No strategy data available');
                                return null;
                            }

                            const performance = strategySource._reportData.performance;

                            const results = {
                                timestamp: new Date().toISOString(),
                                totalPL: performance.all.netProfitPercent + '%',
                                maxEquityDropdown: performance.maxStrategyDrawDownPercent + '%',
                                totalTrades: performance.all.totalTrades.toString(),
                                profitableTrades: performance.all.percentProfitable + '%',
                                profitFactor: performance.all.profitFactor.toString()
                            };

                            console.log('📊 FIXED: Extracted strategy data via API:', results);
                            return results;

                        } catch (error) {
                            console.error('❌ FIXED: Error accessing TradingView API:', error);
                            return null;
                        }
                    },

                    // Check if strategy is still calculating
                    isStrategyLoading: function() {
                        try {
                            const statusView = TradingViewApi._chartWidgetCollection.activeChartWidget._value._model.m_model._activeStrategySource._value._statusView;
                            const statusText = statusView ? statusView._text : "";
                            const isLoading = statusText.includes("loading...");

                            if (isLoading) {
                                console.log('⏳ FIXED: Strategy is still calculating...');
                            } else {
                                console.log('✅ FIXED: Strategy calculation complete');
                            }

                            return isLoading;
                        } catch (error) {
                            console.error('❌ FIXED: Error checking strategy status:', error);
                            return false;
                        }
                    },

                    // Wait for strategy calculation to complete
                    waitForStrategyComplete: async function() {
                        console.log('⏳ FIXED: Waiting for strategy calculation to complete...');

                        let attempts = 0;
                        const maxAttempts = 60; // 30 seconds max wait

                        while (this.isStrategyLoading() && attempts < maxAttempts) {
                            await new Promise(resolve => setTimeout(resolve, 500));
                            attempts++;
                        }

                        if (attempts >= maxAttempts) {
                            console.log('⚠️ FIXED: Timeout waiting for strategy calculation');
                            return false;
                        }

                        // Wait a bit more to ensure data is ready
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        console.log('✅ FIXED: Strategy calculation completed');
                        return true;
                    },

                    // Detect parameters
                    detectParameters: function() {
                        console.log('🔍 FIXED: Detecting strategy parameters...');
                        const parameters = [];

                        const paramDialog = document.querySelector("[data-name='indicator-properties-dialog']");
                        if (!paramDialog) {
                            console.log('❌ FIXED: Strategy settings dialog not found');
                            return parameters;
                        }

                        const numericInputs = paramDialog.querySelectorAll("[inputmode='numeric']");
                        console.log('Found ' + numericInputs.length + ' numeric inputs');

                        numericInputs.forEach((input, index) => {
                            let paramName = 'Parameter ' + (index + 1);

                            // Try to find parameter name
                            const row = input.closest('tr, .row, div');
                            if (row) {
                                const labels = row.querySelectorAll('label, span, div');
                                for (const label of labels) {
                                    const text = label.textContent?.trim();
                                    if (text && text.length > 0 && text.length < 50 && !text.match(/^\d+\.?\d*$/)) {
                                        paramName = text;
                                        break;
                                    }
                                }
                            }

                            parameters.push({
                                name: paramName,
                                element: input,
                                currentValue: input.value,
                                index: index
                            });

                            console.log('✅ FIXED: Found parameter: "' + paramName + '" = ' + input.value);
                        });

                        console.log('🎯 FIXED: Detected ' + parameters.length + ' parameters');
                        return parameters;
                    },

                    // Update parameter with form submission (like working extension)
                    updateParameter: function(paramName, newValue) {
                        console.log('🔄 FIXED: Updating "' + paramName + '" to ' + newValue + '...');

                        const parameters = this.detectParameters();
                        const param = parameters.find(p => p.name === paramName);

                        if (!param) {
                            console.error('❌ FIXED: Parameter "' + paramName + '" not found');
                            console.log('Available parameters:');
                            parameters.forEach((p, i) => {
                                console.log('  ' + (i+1) + '. "' + p.name + '"');
                            });
                            return { success: false, error: 'Parameter not found' };
                        }

                        const input = param.element;
                        const oldValue = input.value;

                        try {
                            // Update the input value
                            input.focus();
                            input.value = newValue.toString();

                            // Trigger events
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            input.dispatchEvent(new Event('change', { bubbles: true }));
                            input.dispatchEvent(new Event('blur', { bubbles: true }));

                            // Submit form (like working extension)
                            setTimeout(() => {
                                const paramDialog = document.querySelector("[data-name='indicator-properties-dialog']");
                                if (paramDialog) {
                                    const forms = paramDialog.getElementsByTagName('form');
                                    if (forms.length > 0) {
                                        console.log('📝 FIXED: Submitting form to apply changes');
                                        forms[0].dispatchEvent(new Event('submit', { bubbles: true }));
                                    } else {
                                        // Try to find Apply/OK button
                                        const buttons = paramDialog.querySelectorAll('button');
                                        for (const btn of buttons) {
                                            const text = btn.textContent?.toLowerCase();
                                            if (text && (text.includes('apply') || text.includes('ok'))) {
                                                console.log('🔘 FIXED: Clicking Apply button');
                                                btn.click();
                                                break;
                                            }
                                        }
                                    }
                                }
                            }, 200);

                            console.log('✅ FIXED: Updated "' + paramName + '": ' + oldValue + ' → ' + newValue);
                            return { success: true, oldValue: oldValue, newValue: newValue };

                        } catch (error) {
                            console.error('❌ FIXED: Error updating parameter:', error);
                            return { success: false, error: error.message };
                        }
                    },

                    // Run optimization with proper API access
                    runOptimization: async function(paramName, values) {
                        console.log('🚀 FIXED: Starting optimization using TradingView API...');
                        console.log('📊 Testing values: [' + values.join(', ') + ']');

                        this.optimizationResults = [];

                        for (let i = 0; i < values.length; i++) {
                            const value = values[i];
                            console.log('\n=== FIXED Iteration ' + (i + 1) + '/' + values.length + ': ' + paramName + ' = ' + value + ' ===');

                            // Update parameter
                            const updateResult = this.updateParameter(paramName, value);

                            if (!updateResult.success) {
                                console.error('❌ FIXED: Failed to update parameter');
                                continue;
                            }

                            // Wait for strategy recalculation
                            const calculationComplete = await this.waitForStrategyComplete();

                            if (!calculationComplete) {
                                console.error('❌ FIXED: Strategy calculation timeout');
                                continue;
                            }

                            // Extract results using TradingView API
                            const results = this.getStrategyData();

                            if (!results) {
                                console.error('❌ FIXED: Failed to extract strategy data');
                                continue;
                            }

                            // Store result
                            const resultEntry = {
                                timestamp: new Date().toISOString(),
                                parameters: {},
                                results: results
                            };

                            resultEntry.parameters[paramName] = value;
                            this.optimizationResults.push(resultEntry);

                            console.log('✅ FIXED: Iteration ' + (i + 1) + ' completed');
                            console.log('📊 Results: P&L=' + results.totalPL + ', Trades=' + results.totalTrades + ', PF=' + results.profitFactor);

                            // Wait between iterations
                            await new Promise(resolve => setTimeout(resolve, 2000));
                        }

                        console.log('🏁 FIXED: Optimization completed!');
                        console.log('📊 Total results collected: ' + this.optimizationResults.length);

                        // Show collected results
                        this.optimizationResults.forEach((result, i) => {
                            console.log('Result ' + (i+1) + ': ' + paramName + '=' + result.parameters[paramName] +
                                       ' → P&L=' + result.results.totalPL +
                                       ', Trades=' + result.results.totalTrades);
                        });

                        if (this.optimizationResults.length > 0) {
                            console.log('💾 FIXED: Creating CSV file...');
                            const csvResult = this.saveCSV();
                            if (csvResult.success) {
                                console.log('✅ CSV file created successfully: ' + csvResult.filename);
                            } else {
                                console.error('❌ CSV creation failed: ' + csvResult.error);
                            }
                        } else {
                            console.log('⚠️ No results to save to CSV');
                        }

                        return {
                            success: true,
                            resultCount: this.optimizationResults.length,
                            csvCreated: this.optimizationResults.length > 0
                        };
                    },

                    // Save CSV function
                    saveCSV: function() {
                        console.log('💾 FIXED: Creating optimization CSV...');

                        if (!this.optimizationResults || this.optimizationResults.length === 0) {
                            console.log('⚠️ No optimization results to save');
                            return { success: false };
                        }

                        try {
                            // Get parameter name from first result
                            const firstResult = this.optimizationResults[0];
                            const paramKeys = Object.keys(firstResult.parameters || {});
                            const paramName = paramKeys[0] || 'Parameter';

                            // Create CSV header with parameter name as first column
                            const csvRows = [paramName + ',Total P&L,Max Equity Dropdown,Total Trades,Profitable Trades,Profit Factor'];

                            this.optimizationResults.forEach((result, i) => {
                                const paramValue = result.parameters ? result.parameters[paramName] : 'N/A';

                                const row = [
                                    paramValue,
                                    result.results.totalPL || 'N/A',
                                    result.results.maxEquityDropdown || 'N/A',
                                    result.results.totalTrades || 'N/A',
                                    result.results.profitableTrades || 'N/A',
                                    result.results.profitFactor || 'N/A'
                                ];
                                csvRows.push(row.join(','));
                            });

                            const csvContent = csvRows.join('\n');

                            const now = new Date();
                            const timestamp = now.getFullYear() + '-' +
                                             String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                             String(now.getDate()).padStart(2, '0') + ' ' +
                                             String(now.getHours()).padStart(2, '0') + '-' +
                                             String(now.getMinutes()).padStart(2, '0') + '-' +
                                             String(now.getSeconds()).padStart(2, '0');
                            const filename = 'TV Fixed Results - ' + timestamp + '.csv';

                            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                            const link = document.createElement('a');
                            const url = URL.createObjectURL(blob);
                            link.setAttribute('href', url);
                            link.setAttribute('download', filename);
                            link.style.visibility = 'hidden';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            URL.revokeObjectURL(url);

                            console.log('✅ FIXED CSV downloaded: ' + filename);
                            console.log('📁 Check your Downloads folder!');

                            return { success: true, filename: filename };

                        } catch (error) {
                            console.error('❌ FIXED CSV creation error:', error);
                            return { success: false, error: error.message };
                        }
                    }
                };

                return { success: true, message: 'Complete optimization system loaded' };
            });

            if (result.success) {
                showStatus('✅ System loaded! Console commands:\n• window.tvOptimizer.detectParameters()\n• window.tvOptimizer.getStrategyData()\n• window.tvOptimizer.runOptimization("ATR Multiplier", [1,2,3])', 'success');
            } else {
                showStatus('❌ Failed to load optimization system', 'error');
            }

        } catch (error) {
            showStatus(`❌ Optimization failed: ${error.message}`, 'error');
        } finally {
            runOptimizationBtn.disabled = false;
            runOptimizationBtn.textContent = 'Run Optimization';
        }
    }

    // Manual CSV Save Function
    async function saveCSV() {
        try {
            showStatus('Saving CSV...', 'info');

            const result = await executeScript(() => {
                if (!window.tvOptimizer || !window.tvOptimizer.optimizationResults || window.tvOptimizer.optimizationResults.length === 0) {
                    return {
                        success: false,
                        message: 'No optimization results found. Run optimization first.'
                    };
                }

                try {
                    const csvResult = window.tvOptimizer.saveCSV();
                    return {
                        success: csvResult.success,
                        message: csvResult.success ? 'CSV saved: ' + csvResult.filename : 'CSV save failed: ' + csvResult.error,
                        filename: csvResult.filename
                    };
                } catch (error) {
                    return {
                        success: false,
                        message: 'Error saving CSV: ' + error.message
                    };
                }
            });

            if (result.success) {
                showStatus(`✅ ${result.message}`, 'success');
            } else {
                showStatus(`❌ ${result.message}`, 'error');
            }
        } catch (error) {
            showStatus(`❌ CSV save failed: ${error.message}`, 'error');
        }
    }

    // Test Optimization Function (with sample data)
    async function testOptimizationWithSample() {
        try {
            showStatus('Testing optimization with sample data...', 'info');

            const result = await executeScript(() => {
                if (!window.tvOptimizer) {
                    return {
                        success: false,
                        message: 'Optimization system not loaded. Click "Run Optimization" first.'
                    };
                }

                // Create sample results for testing
                window.tvOptimizer.optimizationResults = [
                    {
                        timestamp: new Date().toISOString(),
                        parameters: { "ATR Multiplier": 1.0 },
                        results: {
                            totalPL: "-84.50%",
                            maxEquityDropdown: "85.89%",
                            totalTrades: "237",
                            profitableTrades: "28.21%",
                            profitFactor: "0.716"
                        }
                    },
                    {
                        timestamp: new Date().toISOString(),
                        parameters: { "ATR Multiplier": 2.0 },
                        results: {
                            totalPL: "-45.23%",
                            maxEquityDropdown: "62.15%",
                            totalTrades: "156",
                            profitableTrades: "31.45%",
                            profitFactor: "0.892"
                        }
                    },
                    {
                        timestamp: new Date().toISOString(),
                        parameters: { "ATR Multiplier": 3.0 },
                        results: {
                            totalPL: "+12.67%",
                            maxEquityDropdown: "38.92%",
                            totalTrades: "89",
                            profitableTrades: "35.78%",
                            profitFactor: "1.234"
                        }
                    }
                ];

                console.log('📊 Sample optimization results created:', window.tvOptimizer.optimizationResults);

                // Save CSV
                const csvResult = window.tvOptimizer.saveCSV();

                return {
                    success: csvResult.success,
                    message: csvResult.success ? 'Sample CSV created: ' + csvResult.filename : 'CSV creation failed: ' + csvResult.error,
                    filename: csvResult.filename,
                    resultCount: window.tvOptimizer.optimizationResults.length
                };
            });

            if (result.success) {
                showStatus(`✅ ${result.message}\nResults: ${result.resultCount} entries`, 'success');
            } else {
                showStatus(`❌ ${result.message}`, 'error');
            }
        } catch (error) {
            showStatus(`❌ Test optimization failed: ${error.message}`, 'error');
        }
    }

    // Event Listeners
    detectParamsBtn.addEventListener('click', detectParameters);
    testExtractionBtn.addEventListener('click', testDataExtraction);
    runOptimizationBtn.addEventListener('click', runOptimization);

    // Add test extension button functionality
    const testBtn = document.createElement('button');
    testBtn.textContent = 'Test Extension';
    testBtn.style.marginBottom = '10px';
    testBtn.addEventListener('click', testExtension);
    detectParamsBtn.parentNode.insertBefore(testBtn, detectParamsBtn);

    // Add CSV save button
    const saveCSVBtn = document.createElement('button');
    saveCSVBtn.textContent = 'Save CSV Results';
    saveCSVBtn.style.marginTop = '10px';
    saveCSVBtn.style.backgroundColor = '#FF9800';
    saveCSVBtn.addEventListener('click', saveCSV);
    runOptimizationBtn.parentNode.insertBefore(saveCSVBtn, runOptimizationBtn.nextSibling);

    // Add test optimization button
    const testOptBtn = document.createElement('button');
    testOptBtn.textContent = 'Test CSV Creation';
    testOptBtn.style.marginTop = '5px';
    testOptBtn.style.backgroundColor = '#9C27B0';
    testOptBtn.addEventListener('click', testOptimizationWithSample);
    saveCSVBtn.parentNode.insertBefore(testOptBtn, saveCSVBtn.nextSibling);

    // Initial status
    showStatus('Extension ready. Click "Test Extension" to verify setup.', 'info');

    // Debug: Test if buttons are working
    if (detectParamsBtn) {
        console.log('✅ Detect Parameters button found');
    } else {
        console.error('❌ Detect Parameters button NOT found');
    }

    if (testExtractionBtn) {
        console.log('✅ Test Extraction button found');
    } else {
        console.error('❌ Test Extraction button NOT found');
    }

    if (runOptimizationBtn) {
        console.log('✅ Run Optimization button found');
    } else {
        console.error('❌ Run Optimization button NOT found');
    }
});
