// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {

    // Get DOM elements (cached for performance)
    const addParameterBtn = document.querySelector('.add-parameter-btn');
    const startBtn = document.querySelector('.btn-primary');
    const stopBtn = document.querySelector('.btn-danger');
    const parametersContainer = document.querySelector('.parameters-container');
    const totalStepsElement = document.getElementById('totalSteps');

    // Cache frequently accessed elements for performance
    const progressSection = document.getElementById('progress-section');
    const currentStepElement = document.getElementById('currentStep');
    const totalStepsProgressElement = document.getElementById('totalStepsProgress');
    const progressPercentageElement = document.getElementById('progressPercentage');
    const progressFillElement = document.getElementById('progressFill');
    const currentParametersElement = document.getElementById('currentParameters');
    const elapsedTimeElement = document.getElementById('elapsedTime');
    const estimatedTimeElement = document.getElementById('estimatedTime');
    const updateStatusElement = document.getElementById('updateStatus');
    const detectionStatusElement = document.getElementById('detection-status');


    
    // Function to update remove buttons visibility
    function updateRemoveButtons() {
        const removeButtons = document.querySelectorAll('.btn-remove');
        const shouldHide = removeButtons.length <= 1;
        
        removeButtons.forEach(btn => {
            btn.style.display = shouldHide ? 'none' : 'block';
        });
    }
    
    // Function to calculate total steps
    function calculateTotalSteps() {
        const parameterRows = parametersContainer.querySelectorAll('.parameter-row');
        let totalCombinations = 1;

        parameterRows.forEach(row => {
            const paramStart = parseFloat(row.querySelector('.param-start').value) || 0;
            const paramEnd = parseFloat(row.querySelector('.param-end').value) || 0;
            const paramStep = parseFloat(row.querySelector('.param-step').value) || 0;

            if (paramStart && paramEnd && paramStep && paramStep > 0) {
                // FIXED: Use same logic as parameter generation
                let stepCount = 0;
                let value = paramStart;
                while (value <= paramEnd + 0.0001) { // Same epsilon as generation
                    stepCount++;
                    value += paramStep;
                    value = Math.round(value * 100) / 100; // Same rounding as generation
                }

                if (stepCount > 0) {
                    totalCombinations *= stepCount;
                }
            }
        });

        totalStepsElement.textContent = totalCombinations;
    }
    
    // Function to add remove button listener
    function addRemoveButtonListener(button) {
        button.addEventListener('click', function() {
            const row = button.closest('.parameter-row');
            row.remove();
            updateRemoveButtons();
            calculateTotalSteps();
        });
    }
    
    // Function to add input change listeners
    function addInputChangeListeners(row) {
        const inputs = row.querySelectorAll('input[type="number"]');
        inputs.forEach(input => {
            input.addEventListener('input', calculateTotalSteps);
        });
        
        const select = row.querySelector('select');
        if (select) {
            select.addEventListener('change', calculateTotalSteps);
        }
    }
    
    // Function to detect parameters from TradingView
    function detectParametersFromTradingView() {
        return new Promise(async (resolve) => {
            chrome.tabs.query({ active: true, currentWindow: true }, async function(tabs) {
                if (tabs[0] && tabs[0].url && tabs[0].url.includes('tradingview.com')) {
                    const scriptLoaded = await ensureContentScriptLoaded(tabs[0].id);
                    if (!scriptLoaded) {
                        resolve({ error: 'Could not load content script on TradingView page' });
                        return;
                    }

                    chrome.tabs.sendMessage(tabs[0].id, { action: 'detectParameters' }, function(response) {
                        if (chrome.runtime.lastError) {
                            resolve({ error: `Communication error: ${chrome.runtime.lastError.message}` });
                        } else if (response && response.success) {
                            resolve(response.parameters || []);
                        } else {
                            resolve([]);
                        }
                    });
                } else {
                    resolve({ error: 'Not on TradingView page' });
                }
            });
        });
    }

    // Function to inject content script if needed
    function ensureContentScriptLoaded(tabId) {
        return new Promise((resolve) => {
            chrome.tabs.sendMessage(tabId, { action: 'ping' }, function() {
                if (chrome.runtime.lastError) {
                    chrome.scripting.executeScript({
                        target: { tabId: tabId },
                        files: ['content.js']
                    }, function() {
                        if (chrome.runtime.lastError) {
                            resolve(false);
                        } else {
                            setTimeout(() => resolve(true), 1000);
                        }
                    });
                } else {
                    resolve(true);
                }
            });
        });
    }

    // Function to update parameters in TradingView
    function updateParametersInTradingView(parameterUpdates) {
        return new Promise(async (resolve) => {
            chrome.tabs.query({ active: true, currentWindow: true }, async function(tabs) {
                if (tabs[0] && tabs[0].url && tabs[0].url.includes('tradingview.com')) {
                    const scriptLoaded = await ensureContentScriptLoaded(tabs[0].id);
                    if (!scriptLoaded) {
                        resolve({ error: 'Could not load content script on TradingView page' });
                        return;
                    }

                    chrome.tabs.sendMessage(tabs[0].id, {
                        action: 'updateParameters',
                        parameters: parameterUpdates
                    }, function(response) {
                        if (chrome.runtime.lastError) {
                            resolve({ error: `Communication error: ${chrome.runtime.lastError.message}` });
                        } else if (response && response.success) {
                            resolve(response.result);
                        } else {
                            resolve({ error: 'Failed to update parameters - no valid response' });
                        }
                    });
                } else {
                    resolve({ error: 'Not on TradingView page' });
                }
            });
        });
    }

    // Function to extract strategy results from TradingView
    function extractStrategyResults() {
        return new Promise(async (resolve) => {
            chrome.tabs.query({ active: true, currentWindow: true }, async function(tabs) {
                if (tabs[0] && tabs[0].url && tabs[0].url.includes('tradingview.com')) {
                    const scriptLoaded = await ensureContentScriptLoaded(tabs[0].id);
                    if (!scriptLoaded) {
                        resolve({ error: 'Could not load content script on TradingView page' });
                        return;
                    }

                    chrome.tabs.sendMessage(tabs[0].id, {
                        action: 'extractResults'
                    }, function(response) {
                        if (chrome.runtime.lastError) {
                            resolve({ error: `Communication error: ${chrome.runtime.lastError.message}` });
                        } else if (response && response.success) {
                            resolve(response.results);
                        } else {
                            resolve({ error: 'Failed to extract results - no valid response' });
                        }
                    });
                } else {
                    resolve({ error: 'Not on TradingView page' });
                }
            });
        });
    }

    // Function to populate parameter dropdowns
    function populateParameterDropdowns() {
        try {
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
                detectParametersFromTradingView().then(detectedParams => {
                    chrome.storage.local.get(['tvParameters'], function(result) {
                        let parameters = detectedParams.length > 0 ? detectedParams : (result.tvParameters || []);

                        const dropdowns = document.querySelectorAll('.param-name');
                        dropdowns.forEach(dropdown => {
                            const currentValue = dropdown.value;

                            // Clear existing options except the first one
                            while (dropdown.options.length > 1) {
                                dropdown.remove(1);
                            }

                            // Add detected parameters
                            parameters.forEach(param => {
                                const option = document.createElement('option');
                                option.value = param.name;
                                option.textContent = `${param.name} (${param.currentValue})`;
                                dropdown.appendChild(option);
                            });

                            // Add default parameters if no detected parameters
                            if (parameters.length === 0) {
                                const defaultParams = [
                                    'RSI Period', 'MA Length', 'Stop Loss %', 'Take Profit %',
                                    'ATR Period', 'Bollinger Bands Period', 'MACD Fast', 'MACD Slow'
                                ];

                                defaultParams.forEach(paramName => {
                                    const option = document.createElement('option');
                                    option.value = paramName;
                                    option.textContent = paramName;
                                    dropdown.appendChild(option);
                                });
                            }

                            // Restore selection if possible
                            if (currentValue) {
                                dropdown.value = currentValue;
                            }
                        });
                    });
                });
            }
        } catch (error) {
            // Silently handle errors
        }
    }
    
    // Function to create a new parameter row
    function createParameterRow() {
        const row = document.createElement('div');
        row.className = 'parameter-row';
        row.innerHTML = `
            <div class="form-group">
                <select class="param-name">
                    <option value="">Select Parameter</option>
                </select>
            </div>
            <div class="form-group">
                <input type="number" placeholder="Start" class="param-start">
            </div>
            <div class="form-group">
                <input type="number" placeholder="End" class="param-end">
            </div>
            <div class="form-group">
                <input type="number" placeholder="Step" class="param-step">
            </div>
            <div class="form-group">
                <button class="btn btn-remove" title="Remove Parameter">×</button>
            </div>
        `;
        
        return row;
    }
    
    // Add parameter button click handler
    if (addParameterBtn) {
        addParameterBtn.setAttribute('data-initialized', 'true');

        addParameterBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            try {
                const newRow = createParameterRow();
                parametersContainer.appendChild(newRow);
                populateParameterDropdowns();

                const removeBtn = newRow.querySelector('.btn-remove');
                addRemoveButtonListener(removeBtn);
                addInputChangeListeners(newRow);

                updateRemoveButtons();
                calculateTotalSteps();
            } catch (error) {
                // Silently handle errors
            }
        });
    }


    
    // Initialize existing rows
    const existingRows = parametersContainer.querySelectorAll('.parameter-row');
    existingRows.forEach(row => {
        const removeBtn = row.querySelector('.btn-remove');
        addRemoveButtonListener(removeBtn);
        addInputChangeListeners(row);
    });
    
    // Optimization state variables
    let optimizationRunning = false;
    let optimizationInterval;
    let startTime;
    let currentStep = 0;
    let totalSteps = 0;
    let parameterCombinations = [];
    let optimizationResults = []; // Store results for CSV export

    // Function to reopen strategy dialog if it closed
    function reopenStrategyDialog() {
        return new Promise(async (resolve) => {
            chrome.tabs.query({ active: true, currentWindow: true }, async function(tabs) {
                if (tabs[0] && tabs[0].url && tabs[0].url.includes('tradingview.com')) {
                    const scriptLoaded = await ensureContentScriptLoaded(tabs[0].id);
                    if (!scriptLoaded) {
                        resolve({ success: false, error: 'Could not load content script' });
                        return;
                    }

                    chrome.tabs.sendMessage(tabs[0].id, { action: 'reopenDialog' }, function(response) {
                        if (chrome.runtime.lastError) {
                            resolve({ success: false, error: chrome.runtime.lastError.message });
                        } else if (response && response.success) {
                            resolve({ success: true, method: response.result.method });
                        } else {
                            resolve({ success: false, error: 'Failed to reopen dialog' });
                        }
                    });
                } else {
                    resolve({ success: false, error: 'Not on TradingView page' });
                }
            });
        });
    }

    // Function to generate all parameter combinations
    function generateParameterCombinations(parameters) {
        if (parameters.length === 0) return [];

        let combinations = [{}];

        parameters.forEach(param => {
            const newCombinations = [];
            const start = parseFloat(param.start);
            const end = parseFloat(param.end);
            const step = parseFloat(param.step);

            // Generate values for this parameter
            let value = start;
            while (value <= end + 0.0001) { // Add small epsilon for floating point comparison
                const roundedValue = Math.round(value * 100) / 100;
                combinations.forEach(combination => {
                    newCombinations.push({
                        ...combination,
                        [param.name]: roundedValue
                    });
                });
                value += step;
                value = Math.round(value * 100) / 100; // Round to avoid floating point errors
            }

            combinations = newCombinations;
        });

        return combinations;
    }

    // Function to properly escape CSV values that contain commas
    function escapeCSVValue(value) {
        if (value == null || value === undefined) {
            return 'N/A';
        }

        const stringValue = String(value);

        // If the value contains comma, quote, or newline, wrap it in quotes and escape internal quotes
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return '"' + stringValue.replace(/"/g, '""') + '"';
        }

        return stringValue;
    }

    // CSV creation function
    function createCSVDirectlyViaBackground() {
        if (!optimizationResults || optimizationResults.length === 0) {
            return;
        }

        try {
            const firstResult = optimizationResults[0];
            const parameterNames = Object.keys(firstResult.parameters || {});

            const headers = [...parameterNames, 'Total P&L', 'Max Equity Dropdown', 'Total Trades', 'Profitable Trades', 'Profit Factor'];
            const csvRows = [headers.join(',')];

            optimizationResults.forEach(result => {
                const row = [];
                parameterNames.forEach(paramName => {
                    row.push(escapeCSVValue(result.parameters[paramName] || 'N/A'));
                });
                row.push(escapeCSVValue(result.results.totalPL || 'N/A'));
                row.push(escapeCSVValue(result.results.maxEquityDropdown || 'N/A'));
                row.push(escapeCSVValue(result.results.totalTrades || 'N/A'));
                row.push(escapeCSVValue(result.results.profitableTrades || 'N/A'));
                row.push(escapeCSVValue(result.results.profitFactor || 'N/A'));
                csvRows.push(row.join(','));
            });

            const csvContent = csvRows.join('\n');

            const now = new Date();
            const timestamp = now.getFullYear() + '-' +
                             String(now.getMonth() + 1).padStart(2, '0') + '-' +
                             String(now.getDate()).padStart(2, '0') + ' ' +
                             String(now.getHours()).padStart(2, '0') + '-' +
                             String(now.getMinutes()).padStart(2, '0') + '-' +
                             String(now.getSeconds()).padStart(2, '0');
            const filename = 'TV Optimization Results - ' + timestamp + '.csv';

            const downloadFilename = 'test-data/' + filename;

            chrome.runtime.sendMessage({
                action: 'downloadCSV',
                csvContent: csvContent,
                filename: downloadFilename
            }, function() {
                // Silently handle response
            });

        } catch (error) {
            // Silently handle errors
        }
    }

    // Function to update progress bar (batched DOM updates for performance)
    function updateProgress(current, total, currentParams) {
        // Batch DOM updates to reduce reflows
        requestAnimationFrame(() => {
            const percentage = Math.round((current / total) * 100);

            // Update progress elements using cached references
            currentStepElement.textContent = current;
            totalStepsProgressElement.textContent = total;
            progressPercentageElement.textContent = percentage;
            progressFillElement.style.width = percentage + '%';

            // Update current parameters display
            if (currentParams) {
                const paramText = Object.entries(currentParams)
                    .map(([name, value]) => `${name}: ${value}`)
                    .join(', ');
                currentParametersElement.textContent = `Testing: ${paramText}`;
            }

            // Update time information
            const elapsed = Date.now() - startTime;
            const elapsedSeconds = Math.floor(elapsed / 1000);
            const elapsedMinutes = Math.floor(elapsedSeconds / 60);
            const elapsedDisplay = `${elapsedMinutes.toString().padStart(2, '0')}:${(elapsedSeconds % 60).toString().padStart(2, '0')}`;
            elapsedTimeElement.textContent = elapsedDisplay;

            // Calculate estimated time remaining
            if (current > 0) {
                const avgTimePerStep = elapsed / current;
                const remainingSteps = total - current;
                const estimatedRemaining = remainingSteps * avgTimePerStep;
                const estSeconds = Math.floor(estimatedRemaining / 1000);
                const estMinutes = Math.floor(estSeconds / 60);
                const estimatedDisplay = `${estMinutes.toString().padStart(2, '0')}:${(estSeconds % 60).toString().padStart(2, '0')}`;
                estimatedTimeElement.textContent = estimatedDisplay;
            }
        });
    }

    // Function to start optimization
    function startOptimization(parameters) {
        optimizationRunning = true;
        currentStep = 0;
        startTime = Date.now();

        // Clear previous results
        optimizationResults = [];

        // Generate all parameter combinations
        parameterCombinations = generateParameterCombinations(parameters);
        totalSteps = parameterCombinations.length;

        // Pre-cache parameters before starting optimization
        chrome.tabs.query({active: true, currentWindow: true}, async function(tabs) {
            try {
                await chrome.tabs.sendMessage(tabs[0].id, { action: 'cacheParameters' });
            } catch (error) {
                // Silently handle pre-caching errors
            }
        });

        // Show progress section using cached element
        progressSection.style.display = 'block';

        // Update button states
        startBtn.textContent = 'Optimizing...';
        startBtn.disabled = true;
        stopBtn.disabled = false;

        // Initialize progress
        updateProgress(0, totalSteps, null);

        // Start DIRECT optimization (no interval delays)
        processNextStep();

        async function processNextStep() {
            if (currentStep >= totalSteps || !optimizationRunning) {
                completeOptimization();
                return;
            }

            currentStep++;
            const currentParams = parameterCombinations[currentStep - 1];

            updateProgress(currentStep, totalSteps, currentParams);

            if (updateStatusElement) {
                requestAnimationFrame(() => {
                    updateStatusElement.style.display = 'flex';
                });
            }

            try {
                const updateResult = await updateParametersInTradingView(currentParams);

                if (updateResult.error) {
                    if (updateStatusElement) {
                        updateStatusElement.querySelector('.status-text').textContent = `Error: ${updateResult.error}`;
                        updateStatusElement.style.borderColor = '#dc3545';
                    }
                    setTimeout(processNextStep, 300);
                    return;
                } else if (updateResult.updatedCount === 0) {
                    if (updateStatusElement) {
                        updateStatusElement.querySelector('.status-text').textContent = 'No parameters updated';
                        updateStatusElement.style.borderColor = '#ffc107';
                    }
                    setTimeout(processNextStep, 300);
                    return;
                }

                // Handle recalculation result and dialog reopening if needed
                if (updateResult.success) {
                    if (updateStatusElement) {
                        updateStatusElement.querySelector('.status-text').textContent = 'Strategy recalculation completed ✓';
                        updateStatusElement.style.borderColor = '#28a745';
                    }

                    // Handle dialog reopening if it closed after Apply button
                    if (updateResult.needsReopen && !updateResult.dialogOpen) {
                        console.log('🔄 Dialog closed after Apply - attempting to reopen for next iteration...');

                        if (updateStatusElement) {
                            updateStatusElement.querySelector('.status-text').textContent = 'Reopening strategy dialog...';
                            updateStatusElement.style.borderColor = '#17a2b8';
                        }

                        // Attempt to reopen the dialog for next iteration
                        const reopenResult = await reopenStrategyDialog();
                        if (!reopenResult.success) {
                            console.log('⚠️ Failed to reopen dialog - may need manual intervention');
                            if (updateStatusElement) {
                                updateStatusElement.querySelector('.status-text').textContent = 'Failed to reopen dialog';
                                updateStatusElement.style.borderColor = '#dc3545';
                            }
                        } else {
                            console.log('✅ Dialog reopened successfully');
                            if (updateStatusElement) {
                                updateStatusElement.querySelector('.status-text').textContent = 'Dialog reopened ✓';
                                updateStatusElement.style.borderColor = '#28a745';
                            }
                        }
                    }

                    // Log status for debugging
                    if (!updateResult.strategyTesterOpen) {
                        console.log('⚠️ Strategy tester closed - results may not be available');
                    }
                } else {
                    if (updateStatusElement) {
                        updateStatusElement.querySelector('.status-text').textContent = 'Recalculation completed with warnings';
                        updateStatusElement.style.borderColor = '#ffc107';
                    }
                }

                if (updateStatusElement) {
                    updateStatusElement.querySelector('.status-text').textContent = `Collecting results for step ${currentStep}...`;
                    updateStatusElement.style.borderColor = '#28a745';
                }

                // Wait longer for strategy recalculation when multiple parameters are involved
                const waitTime = Object.keys(currentParams).length > 1 ? 2000 : 800;
                await new Promise(resolve => setTimeout(resolve, waitTime));

                let strategyResults = await extractStrategyResults();

                const resultEntry = {
                    timestamp: new Date().toISOString(),
                    step: currentStep,
                    parameters: currentParams,
                    results: strategyResults && !strategyResults.error ? strategyResults : {
                        totalPL: 'N/A',
                        maxEquityDropdown: 'N/A',
                        totalTrades: 'N/A',
                        profitableTrades: 'N/A',
                        profitFactor: 'N/A'
                    }
                };

                optimizationResults.push(resultEntry);

                if (updateStatusElement) {
                    updateStatusElement.style.display = 'none';
                }

                setTimeout(processNextStep, 100);

            } catch (error) {
                if (updateStatusElement) {
                    updateStatusElement.querySelector('.status-text').textContent = 'Error in optimization step';
                    updateStatusElement.style.borderColor = '#dc3545';
                }
                setTimeout(processNextStep, 300);
            }
        }
    }

    // Function to complete optimization
    function completeOptimization() {
        clearInterval(optimizationInterval);
        optimizationRunning = false;

        // Update UI
        startBtn.textContent = 'Start Optimization';
        startBtn.disabled = false;
        stopBtn.disabled = true;

        // Show completion message using cached elements
        currentParametersElement.textContent = 'Optimization completed! Creating CSV file...';
        progressFillElement.style.background = 'linear-gradient(90deg, #28a745 0%, #20c997 100%)';

        setTimeout(() => {
            if (optimizationResults.length > 0) {
                createCSVDirectlyViaBackground();
            }
        }, 200);
    }

    // Function to stop optimization
    function stopOptimization() {
        if (optimizationRunning) {
            optimizationRunning = false;

            // Update UI
            startBtn.textContent = 'Start Optimization';
            startBtn.disabled = false;
            stopBtn.disabled = true;

            currentParametersElement.textContent = 'Optimization stopped by user';
            progressFillElement.style.background = 'linear-gradient(90deg, #dc3545 0%, #c82333 100%)';
        }
    }

    // Start button click handler
    startBtn.addEventListener('click', function() {
        const parameterRows = parametersContainer.querySelectorAll('.parameter-row');

        // Collect all parameters
        const parameters = [];
        parameterRows.forEach(row => {
            const paramName = row.querySelector('.param-name').value;
            const paramStart = parseFloat(row.querySelector('.param-start').value);
            const paramEnd = parseFloat(row.querySelector('.param-end').value);
            const paramStep = parseFloat(row.querySelector('.param-step').value);

            if (paramName && !isNaN(paramStart) && !isNaN(paramEnd) && !isNaN(paramStep) && paramStep > 0) {
                parameters.push({
                    name: paramName,
                    start: paramStart,
                    end: paramEnd,
                    step: paramStep
                });
            }
        });

        if (parameters.length > 0) {
            startOptimization(parameters);
        }
    });
    
    // Stop button click handler
    stopBtn.addEventListener('click', function() {
        stopOptimization();
    });
    
    // Function to show/hide detection status using cached element
    function showDetectionStatus(message, type = 'info') {
        const statusText = detectionStatusElement?.querySelector('.status-text');

        if (detectionStatusElement && statusText) {
            statusText.textContent = message;
            detectionStatusElement.className = `detection-status ${type}`;
            detectionStatusElement.style.display = 'flex';

            // Auto-hide after 3 seconds for success/error messages
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    detectionStatusElement.style.display = 'none';
                }, 3000);
            }
        }
    }

    function hideDetectionStatus() {
        if (detectionStatusElement) {
            detectionStatusElement.style.display = 'none';
        }
    }

    // Function to auto-detect parameters on startup
    function autoDetectParameters() {
        showDetectionStatus('Auto-detecting parameters...', 'info');

        detectParametersFromTradingView().then(result => {
            if (Array.isArray(result) && result.length > 0) {
                showDetectionStatus(`✓ Auto-detected ${result.length} parameters`, 'success');

                chrome.storage.local.set({ 'tvParameters': result }, function() {
                    populateParameterDropdowns();
                });
            } else {
                hideDetectionStatus();
                populateParameterDropdowns();
            }
        }).catch(() => {
            hideDetectionStatus();
            populateParameterDropdowns();
        });
    }

    // Initial setup
    updateRemoveButtons();
    calculateTotalSteps();

    // Initialize button states
    stopBtn.disabled = true;

    // Auto-detect parameters on startup
    autoDetectParameters();

    // Fallback click listener
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('add-parameter-btn') && !e.target.hasAttribute('data-initialized')) {
            e.preventDefault();
            e.stopPropagation();

            try {
                const newRow = createParameterRow();
                parametersContainer.appendChild(newRow);
                populateParameterDropdowns();

                const removeBtn = newRow.querySelector('.btn-remove');
                addRemoveButtonListener(removeBtn);
                addInputChangeListeners(newRow);

                updateRemoveButtons();
                calculateTotalSteps();
            } catch (error) {
                // Silently handle errors
            }
        }
    });
});


